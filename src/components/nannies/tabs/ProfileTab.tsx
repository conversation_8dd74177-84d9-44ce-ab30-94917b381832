import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { languageLevels, languages as languageOptions } from "@/lib/staticData";
import { getLanguageLabel } from "@/lib/tableUtils";
import {
  firstAidOptions,
  lessonFormatOptions,
  musicalInstrumentOptions,
  musicTheoryOptions,
  positionOptions,
  socialMediaPlatformOptions,
  specializationOptions,
  startDateOptions,
} from "@/schemas/FormSchema";
import { getText } from "@/utils/form-utils";
import { el } from "date-fns/locale"; // Import Greek locale
import {
  BookOpen,
  Briefcase,
  Calendar as CalendarIcon,
  Heart,
  Music,
  Plus,
  Trash2,
  User,
} from "lucide-react";
import { useMemo, useState } from "react";
import { DateRange, DayModifiers } from "react-day-picker";
import { Candidate } from "../models/Candidate";

interface ProfileTabProps {
  candidate: Candidate;
  editedCandidate: Candidate | null;
  editMode: boolean;
  isNannyUser?: boolean;
  handleInputChange: (
    field: string,
    value: any // Using any to accommodate complex objects like languages
  ) => void;
  handlePositionInterestToggle: (position: string) => void;
  onAddAvailableRange: (range: DateRange) => void;
  onRemoveAvailableRange: (index: number) => void;
  onMarkRangeAsUnavailable: (range: DateRange) => void;
  onRemoveUnavailableDateRange: (range: DateRange) => void;
  onToggleUnavailableDate: (date: Date) => void;
}
// Duration options
const durationOptions = [
  { id: "long-term", label: "Long-Term" },
  { id: "short-term", label: "Short-Term" },
];

// Schedule options
const scheduleOptions = [
  { id: "full-time", label: "Full-Time" },
  { id: "part-time", label: "Part-Time" },
];

const positionTypes = [
  { id: "live-in", label: "Live-In" },
  { id: "live-out", label: "Live-Out" },
];

const ProfileTab = ({
  candidate,
  editedCandidate,
  editMode,
  handleInputChange,
  handlePositionInterestToggle,
  isNannyUser = false,
  onAddAvailableRange,
  onRemoveAvailableRange,
  onMarkRangeAsUnavailable,
  onRemoveUnavailableDateRange,
  onToggleUnavailableDate,
}: ProfileTabProps) => {
  // Handle language changes
  const handleLanguageChange = (
    index: number,
    field: "language" | "level",
    value: string
  ) => {
    if (!editedCandidate) return;

    const updatedLanguages = [...(editedCandidate.form_data.languages || [])];
    if (!updatedLanguages[index]) {
      updatedLanguages[index] = { language: "", level: "" };
    }

    updatedLanguages[index] = {
      ...updatedLanguages[index],
      [field]: value,
    };

    // We need to update both the top-level languages field and form_data.languages
    // Instead of using handleInputChange, we'll directly call the parent component's function
    // that handles language changes
    if (editedCandidate.form_data) {
      // Make sure form_data.languages exists and is an array
      const formDataLanguages = Array.isArray(
        editedCandidate.form_data.languages
      )
        ? [...editedCandidate.form_data.languages]
        : [];

      // Update or add the language in form_data.languages
      if (formDataLanguages[index]) {
        formDataLanguages[index] = {
          ...formDataLanguages[index],
          [field]: value,
        };
      } else {
        // If the index doesn't exist in form_data.languages, add it
        while (formDataLanguages.length < index) {
          formDataLanguages.push({ language: "", level: "" });
        }
        formDataLanguages.push({
          ...updatedLanguages[index],
        });
      }

      // Update both fields
      handleInputChange("languages", updatedLanguages);
      handleInputChange("form_data.languages", formDataLanguages);
    } else {
      // If form_data doesn't exist, just update the top-level field
      handleInputChange("languages", updatedLanguages);
    }
  };

  // Add a new language
  const handleAddLanguage = () => {
    if (!editedCandidate) return;

    const updatedLanguages = [
      ...(editedCandidate.form_data.languages || []),
      { language: "", level: "" },
    ];

    // Update both top-level languages and form_data.languages
    if (editedCandidate.form_data) {
      const formDataLanguages = Array.isArray(
        editedCandidate.form_data.languages
      )
        ? [...editedCandidate.form_data.languages, { language: "", level: "" }]
        : [{ language: "", level: "" }];

      handleInputChange("form_data.languages", formDataLanguages);
    } else {
      handleInputChange("languages", updatedLanguages);
    }
  };

  // Remove a language
  const handleRemoveLanguage = (index: number) => {
    if (!editedCandidate) return;

    const updatedLanguages = [...(editedCandidate.form_data.languages || [])];
    updatedLanguages.splice(index, 1);

    // Update both top-level languages and form_data.languages
    if (
      editedCandidate.form_data &&
      Array.isArray(editedCandidate.form_data.languages)
    ) {
      const formDataLanguages = [...editedCandidate.form_data.languages];
      if (index < formDataLanguages.length) {
        formDataLanguages.splice(index, 1);
      }

      handleInputChange("languages", updatedLanguages);
      handleInputChange("form_data.languages", formDataLanguages);
    } else {
      handleInputChange("languages", updatedLanguages);
    }
  };

  // Handle duration interest toggle
  const handleDurationInterestToggle = (durationId: string) => {
    if (!editedCandidate) return;

    const currentInterests = [
      ...(editedCandidate.form_data.duration_interests || []),
    ];
    const index = currentInterests.indexOf(durationId);

    if (index === -1) {
      // Add interest
      currentInterests.push(durationId);
    } else {
      // Remove interest
      currentInterests.splice(index, 1);
    }

    // Update duration interests
    handleInputChange("form_data.duration_interests", currentInterests);
  };

  // Handle schedule interest toggle
  const handleScheduleInterestToggle = (scheduleId: string) => {
    if (!editedCandidate) return;

    const currentInterests = [
      ...(editedCandidate.form_data.schedule_interests || []),
    ];
    const index = currentInterests.indexOf(scheduleId);

    if (index === -1) {
      // Add interest
      currentInterests.push(scheduleId);
    } else {
      // Remove interest
      currentInterests.splice(index, 1);
    }

    // Update schedule interests
    handleInputChange("form_data.schedule_interests", currentInterests);
  };

  // --- Social Media Handlers ---
  const handleSocialMediaFieldChange = (
    index: number,
    fieldKey: "platform" | "handle",
    value: string
  ) => {
    if (!editedCandidate) return;
    const updatedSocialMedia = [
      ...(editedCandidate.form_data.socialMedia || []),
    ];
    if (!updatedSocialMedia[index]) {
      updatedSocialMedia[index] = { platform: "", handle: "" };
    }
    updatedSocialMedia[index][fieldKey] = value;
    handleInputChange("form_data.socialMedia", updatedSocialMedia);
  };

  const addSocialMediaEntry = () => {
    if (!editedCandidate) return;
    const updatedSocialMedia = [
      ...(editedCandidate.form_data.socialMedia || []),
      { platform: "", handle: "" },
    ];
    handleInputChange("form_data.socialMedia", updatedSocialMedia);
  };

  const removeSocialMediaEntry = (index: number) => {
    if (!editedCandidate) return;
    const updatedSocialMedia = [
      ...(editedCandidate.form_data.socialMedia || []),
    ];
    updatedSocialMedia.splice(index, 1);
    handleInputChange("form_data.socialMedia", updatedSocialMedia);
  };

  // --- Allergies Handlers ---
  const handleAllergyChange = (
    fieldKey: "hasAllergies" | "allergyDetails",
    value: string
  ) => {
    if (!editedCandidate) return;
    const currentAllergies = editedCandidate.form_data.allergies || {
      hasAllergies: "no",
      allergyDetails: "",
    };
    const updatedAllergies = { ...currentAllergies, [fieldKey]: value };

    if (fieldKey === "hasAllergies" && value === "no") {
      updatedAllergies.allergyDetails = "";
    }
    handleInputChange("form_data.allergies", updatedAllergies);
  };

  // Handle musical instruments toggle
  const handleMusicalInstrumentsToggle = (instrumentId: string) => {
    if (!editedCandidate) return;

    const currentInstruments = [
      ...(editedCandidate.form_data.musical_instruments || []),
    ];
    const index = currentInstruments.indexOf(instrumentId);

    if (index === -1) {
      // Add instrument
      currentInstruments.push(instrumentId);
    } else {
      // Remove instrument
      currentInstruments.splice(index, 1);
    }

    // Update musical instruments
    handleInputChange("form_data.musical_instruments", currentInstruments);
  };

  // Handle music theory toggle
  const handleMusicTheoryToggle = (theoryId: string) => {
    if (!editedCandidate) return;

    const currentTheory = [...(editedCandidate.form_data.music_theory || [])];
    const index = currentTheory.indexOf(theoryId);

    if (index === -1) {
      // Add theory
      currentTheory.push(theoryId);
    } else {
      // Remove theory
      currentTheory.splice(index, 1);
    }

    // Update music theory
    handleInputChange("form_data.music_theory", currentTheory);
  };

  // Handle lesson format toggle
  const handleLessonFormatToggle = (formatId: string) => {
    if (!editedCandidate) return;

    const currentFormats = [...(editedCandidate.form_data.lesson_format || [])];
    const index = currentFormats.indexOf(formatId);

    if (index === -1) {
      // Add format
      currentFormats.push(formatId);
    } else {
      // Remove format
      currentFormats.splice(index, 1);
    }

    // Update lesson format
    handleInputChange("form_data.lesson_format", currentFormats);
  };

  // Handle specialization types toggle
  const handleSpecializationTypesToggle = (specializationId: string) => {
    if (!editedCandidate) return;

    const currentTypes = [...(editedCandidate.form_data.specialization_types || [])];
    const index = currentTypes.indexOf(specializationId);

    if (index === -1) {
      // Add specialization type
      currentTypes.push(specializationId);
    } else {
      // Remove specialization type
      currentTypes.splice(index, 1);
    }

    // Update specialization types
    handleInputChange("form_data.specialization_types", currentTypes);
  };

  // --- Availability Calendar Logic ---
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>(
    undefined
  );

  const availableDateRanges: DateRange[] = useMemo(() => {
    return (editedCandidate?.schedule?.availableRanges || [])
      .map((range) => {
        // Ensure dates are parsed as local, not UTC, by appending time
        const from = new Date(range.from + "T00:00:00");
        const to = new Date(range.to + "T00:00:00");
        if (!isNaN(from.valueOf()) && !isNaN(to.valueOf())) {
          return { from, to };
        }
        return null;
      })
      .filter(Boolean) as DateRange[];
  }, [editedCandidate?.schedule?.availableRanges]);

  const unavailableDatesForPicker: Date[] = useMemo(() => {
    return (editedCandidate?.schedule?.explicitlyUnavailable || [])
      .map((dateStr) => {
        const date = new Date(dateStr + "T00:00:00");
        return isNaN(date.valueOf()) ? null : date;
      })
      .filter(Boolean) as Date[];
  }, [editedCandidate?.schedule?.explicitlyUnavailable]);

  // Helper to group sorted dates into ranges
  const groupDatesIntoRanges = (dates: Date[]): DateRange[] => {
    if (!dates || dates.length === 0) {
      return [];
    }
    // Dates are already Date objects from unavailableDatesForPicker, ensure they are sorted
    const sortedDates = [...dates].sort((a, b) => a.getTime() - b.getTime());

    const ranges: DateRange[] = [];
    if (sortedDates.length === 0) return ranges;

    let currentRangeStart = sortedDates[0];
    let currentRangeEnd = sortedDates[0];

    for (let i = 1; i < sortedDates.length; i++) {
      const expectedNextDay = new Date(currentRangeEnd);
      expectedNextDay.setDate(expectedNextDay.getDate() + 1);

      // Compare year, month, and day
      if (
        sortedDates[i].getFullYear() === expectedNextDay.getFullYear() &&
        sortedDates[i].getMonth() === expectedNextDay.getMonth() &&
        sortedDates[i].getDate() === expectedNextDay.getDate()
      ) {
        currentRangeEnd = sortedDates[i]; // Extend the current range
      } else {
        // Finalize the current range and start a new one
        ranges.push({ from: currentRangeStart, to: currentRangeEnd });
        currentRangeStart = sortedDates[i];
        currentRangeEnd = sortedDates[i];
      }
    }
    // Add the last processed range
    ranges.push({ from: currentRangeStart, to: currentRangeEnd });
    return ranges;
  };

  const unavailableDateRangesForDisplay: DateRange[] = useMemo(() => {
    return groupDatesIntoRanges(unavailableDatesForPicker);
  }, [unavailableDatesForPicker]);

  const handleDayClick = (day: Date, modifiers: DayModifiers) => {
    if (editMode && !modifiers.disabled) {
      onToggleUnavailableDate(day);
    }
  };

  const handleMarkRangeAvailable = () => {
    if (selectedRange && selectedRange.from && selectedRange.to && editMode) {
      onAddAvailableRange(selectedRange);
      setSelectedRange(undefined); // Reset after adding
    }
  };

  const handleMarkRangeUnavailableAction = () => {
    if (selectedRange && selectedRange.from && selectedRange.to && editMode) {
      onMarkRangeAsUnavailable(selectedRange);
      setSelectedRange(undefined); // Reset after adding
    }
  };

  const availabilityModifiers = {
    available: availableDateRanges,
    unavailable: unavailableDatesForPicker,
    // Highlight the currently selected range for adding
    ...(selectedRange &&
      selectedRange.from && { currentSelection: selectedRange }),
  };

  const availabilityModifiersStyles = {
    available: {
      backgroundColor: "var(--success-light, #d1fae5)", // Light green fallback
      color: "var(--success-dark, #065f46)", // Dark green text fallback
      fontWeight: 500, // Make it slightly bolder
    },
    unavailable: {
      backgroundColor: "var(--destructive-light, #fee2e2)", // Light red fallback
      color: "var(--destructive-dark, #991b1b)", // Dark red text fallback
      textDecoration: "line-through",
    },
    currentSelection: {
      backgroundColor: "var(--primary-light)", // Or your theme's primary color
    },
  };
  // --- End Availability Calendar Logic ---

  return (
    <div className="w-full grid grid-cols-1 md:grid-cols-[320px_1fr] gap-8 ">
      {/* Calendar Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">Διαθεσιμότητα</h3>
          </div>
        </div>

        <Card variant="accent" className="p-4">
          {editMode ? (
            <>
              <p className="text-xs text-muted-foreground mb-2">
                Επιλέξτε ένα εύρος ημερομηνιών και πατήστε "Ορισμός ως
                Διαθέσιμο" ή "Ορισμός ως Μη Διαθέσιμο"
              </p>
              <div className="flex justify-center">
                <Calendar
                  mode="range"
                  selected={selectedRange}
                  onSelect={setSelectedRange}
                  modifiers={availabilityModifiers}
                  modifiersStyles={availabilityModifiersStyles}
                  className="rounded-md border"
                  numberOfMonths={1}
                  locale={el} // Use Greek locale for month/day names
                />
              </div>
              {selectedRange?.from && (
                <div className="mt-2 space-y-2">
                  <Button
                    onClick={handleMarkRangeAvailable}
                    className="w-full"
                    disabled={!selectedRange.to}
                    size="sm"
                    variant="default" // Or your preferred styling for "available"
                  >
                    Ορισμός Εύρους ως Διαθέσιμο
                  </Button>
                  <Button
                    onClick={handleMarkRangeUnavailableAction}
                    className="w-full"
                    disabled={!selectedRange.to}
                    size="sm"
                    variant="destructive" // Or your preferred styling for "unavailable"
                  >
                    Ορισμός Εύρους ως Μη Διαθέσιμο
                  </Button>
                </div>
              )}
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-1">
                  Τρέχουσες Διαθέσιμες Περίοδοι:
                </h4>
                {availableDateRanges.length > 0 ? (
                  <ul className="space-y-1 text-xs">
                    {availableDateRanges.map((range, index) => (
                      <li
                        key={index}
                        className="flex justify-between items-center p-1 bg-muted/30 rounded"
                      >
                        <span>
                          {range.from?.toLocaleDateString()} -{" "}
                          {range.to?.toLocaleDateString()}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onRemoveAvailableRange(index)}
                          className="text-destructive hover:bg-destructive/10 h-6 w-6"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Δεν έχουν οριστεί διαθέσιμες περίοδοι.
                  </p>
                )}
              </div>
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-1">
                  Μη Διαθέσιμες Περίοδοι:
                </h4>
                {unavailableDateRangesForDisplay.length > 0 ? (
                  <ul className="space-y-1 text-xs">
                    {unavailableDateRangesForDisplay.map((range, index) => (
                      <li
                        key={index}
                        className="flex justify-between items-center p-1 bg-muted/30 rounded"
                      >
                        <span>
                          {range.from?.toLocaleDateString()}
                          {range.from?.getTime() !== range.to?.getTime() &&
                            ` - ${range.to?.toLocaleDateString()}`}
                        </span>
                        {editMode && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => onRemoveUnavailableDateRange(range)}
                            className="text-destructive hover:bg-destructive/10 h-6 w-6"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Δεν έχουν οριστεί μη διαθέσιμες περίοδοι.
                  </p>
                )}
              </div>
            </>
          ) : (
            // Display mode for availability
            <>
              <div className="flex justify-center">
                <Calendar
                  lang="el"
                  mode="multiple" // Using multiple to display modifiers without interaction
                  selected={[]} // Not for selection in display mode
                  modifiers={availabilityModifiers}
                  modifiersStyles={availabilityModifiersStyles}
                  className="rounded-md border"
                  disabled // Disable interaction
                  locale={el} // Use Greek locale for month/day names
                />
              </div>
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-1">
                  Διαθέσιμες Περίοδοι:
                </h4>
                {availableDateRanges.length > 0 ? (
                  <ul className="space-y-1 text-xs">
                    {availableDateRanges.map((range, index) => (
                      <li key={index} className="p-1 bg-muted/30 rounded">
                        {range.from?.toLocaleDateString()} -{" "}
                        {range.to?.toLocaleDateString()}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Δεν έχουν οριστεί διαθέσιμες περίοδοι.
                  </p>
                )}
              </div>
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-1">
                  Μη Διαθέσιμες Περίοδοι:
                </h4>
                {unavailableDateRangesForDisplay.length > 0 ? (
                  <ul className="space-y-1 text-xs">
                    {unavailableDateRangesForDisplay.map((range, index) => (
                      <li key={index} className="p-1 bg-muted/30 rounded">
                        {range.from?.toLocaleDateString()}
                        {range.from?.getTime() !== range.to?.getTime() &&
                          ` - ${range.to?.toLocaleDateString()}`}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs text-muted-foreground">
                    Δεν έχουν οριστεί μη διαθέσιμες περίοδοι.
                  </p>
                )}
              </div>
            </>
          )}

          <div className="mt-4 space-y-2">
            {editMode ? (
              <>
                <div className="flex justify-between items-center">
                  <span>Νυχτερινή βάρδια:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.night_shift_availability ===
                        "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.night_shift_availability",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Ταξίδια:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.travel_availability === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.travel_availability",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Κατοικίδια:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.comfortable_with_pets ===
                        "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.comfortable_with_pets",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Οδήγηση:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.experienced_driver === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.experienced_driver",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Κολύμβηση:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.experienced_swimmer === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.experienced_swimmer",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Ασφάλιση:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.insurance === "yes"
                        ? "Ναι"
                        : editedCandidate?.form_data?.insurance === "maybe"
                          ? "Ίσως"
                          : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.insurance",
                        value === "Ναι" ? "yes" : value === "Ίσως" ? "maybe" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                      <SelectItem value="Ίσως">Ίσως</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {(editedCandidate?.form_data?.insurance === "yes" || editedCandidate?.form_data?.insurance === "maybe") && (
                  <div className="flex justify-between items-center">
                    <span>Τύπος Ασφάλισης:</span>
                    <Select
                      value={
                        editedCandidate?.form_data?.insurance_type === "ergosimo"
                          ? "Εργόσημα"
                          : editedCandidate?.form_data?.insurance_type === "stamps"
                            ? "Ένσημα"
                            : editedCandidate?.form_data?.insurance_type === "booklet"
                              ? "Μπλοκάκι"
                              : "Εργόσημα"
                      }
                      onValueChange={(value) =>
                        handleInputChange(
                          "form_data.insurance_type",
                          value === "Εργόσημα" ? "ergosimo" : value === "Ένσημα" ? "stamps" : "booklet"
                        )
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Εργόσημα">Εργόσημα</SelectItem>
                        <SelectItem value="Ένσημα">Ένσημα</SelectItem>
                        <SelectItem value="Μπλοκάκι">Μπλοκάκι</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span>Δίπλωμα Οδήγησης:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.driving_license === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.driving_license",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Όχημα:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.vehicle === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.vehicle",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {editedCandidate?.form_data?.vehicle === "yes" && (
                  <div className="flex justify-between items-center">
                    <span>Τύπος Οχήματος:</span>
                    <Input
                      value={editedCandidate?.form_data?.vehicle_type || ""}
                      onChange={(e) =>
                        handleInputChange("form_data.vehicle_type", e.target.value)
                      }
                      placeholder="π.χ. Αυτοκίνητο, Μοτοσικλέτα"
                      className="w-48"
                    />
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span>Καπνιστής:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.smoker === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.smoker",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between items-center">
                  <span>Ειδίκευση:</span>
                  <Select
                    value={
                      editedCandidate?.form_data?.specialization === "yes"
                        ? "Ναι"
                        : "Όχι"
                    }
                    onValueChange={(value) =>
                      handleInputChange(
                        "form_data.specialization",
                        value === "Ναι" ? "yes" : "no"
                      )
                    }
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Ναι">Ναι</SelectItem>
                      <SelectItem value="Όχι">Όχι</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {editedCandidate?.form_data?.specialization === "yes" && (
                  <div className="mt-4">
                    <span className="text-sm font-medium mb-2 block">Τύποι Ειδίκευσης:</span>
                    <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto border p-3 rounded-md">
                      {specializationOptions.map((option) => (
                        <div key={option.id} className="flex items-start space-x-2">
                          <Checkbox
                            id={`specialization-${option.id}`}
                            checked={editedCandidate?.form_data?.specialization_types?.includes(option.id) || false}
                            onCheckedChange={() => handleSpecializationTypesToggle(option.id)}
                          />
                          <Label htmlFor={`specialization-${option.id}`} className="text-xs cursor-pointer">
                            {getText(option.label, "el")}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="flex justify-between">
                  <span>Νυχτερινή βάρδια:</span>
                  <span className="font-medium">
                    {candidate.form_data.night_shift_availability === "yes"
                      ? "Ναι"
                      : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Ταξίδια:</span>
                  <span className="font-medium">
                    {candidate.form_data.travel_availability === "yes"
                      ? "Ναι"
                      : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Κατοικίδια:</span>
                  <span className="font-medium">
                    {candidate.form_data.comfortable_with_pets === "yes"
                      ? "Ναι"
                      : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Οδήγηση:</span>
                  <span className="font-medium">
                    {candidate.form_data.experienced_driver === "yes"
                      ? "Ναι"
                      : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Κολύμβηση:</span>
                  <span className="font-medium">
                    {candidate.form_data.experienced_swimmer === "yes"
                      ? "Ναι"
                      : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Ασφάλιση:</span>
                  <span className="font-medium">
                    {candidate.form_data.insurance === "yes"
                      ? "Ναι"
                      : candidate.form_data.insurance === "maybe"
                        ? "Ίσως"
                        : "Όχι"}
                  </span>
                </div>
                {(candidate.form_data.insurance === "yes" || candidate.form_data.insurance === "maybe") && candidate.form_data.insurance_type && (
                  <div className="flex justify-between">
                    <span>Τύπος Ασφάλισης:</span>
                    <span className="font-medium">
                      {candidate.form_data.insurance_type === "ergosimo"
                        ? "Εργόσημα"
                        : candidate.form_data.insurance_type === "stamps"
                          ? "Ένσημα"
                          : candidate.form_data.insurance_type === "booklet"
                            ? "Μπλοκάκι"
                            : candidate.form_data.insurance_type}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Δίπλωμα Οδήγησης:</span>
                  <span className="font-medium">
                    {candidate.form_data.driving_license === "yes" ? "Ναι" : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Όχημα:</span>
                  <span className="font-medium">
                    {candidate.form_data.vehicle === "yes" ? "Ναι" : "Όχι"}
                    {candidate.form_data.vehicle === "yes" && candidate.form_data.vehicle_type && (
                      <span className="text-sm text-muted-foreground ml-2">
                        ({candidate.form_data.vehicle_type})
                      </span>
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Καπνιστής:</span>
                  <span className="font-medium">
                    {candidate.form_data.smoker === "yes" ? "Ναι" : "Όχι"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Ειδίκευση:</span>
                  <span className="font-medium">
                    {candidate.form_data.specialization === "yes" ? "Ναι" : "Όχι"}
                  </span>
                </div>
                {candidate.form_data.specialization === "yes" && candidate.form_data.specialization_types && candidate.form_data.specialization_types.length > 0 && (
                  <div className="flex justify-between">
                    <span>Τύποι Ειδίκευσης:</span>
                    <div className="font-medium text-right max-w-xs">
                      <div className="flex flex-wrap gap-1 justify-end">
                        {candidate.form_data.specialization_types.map((typeId, index) => {
                          const specializationOption = [
                            { id: "maternity_nurse", label: "Μαία" },
                            { id: "maternity_nanny", label: "Maternity Nanny" },
                            { id: "nursery_assistant", label: "Βοηθός Βρεφονηπιοκόμου" },
                            { id: "nursery_childcare", label: "Βρεφονηπιοκόμος" },
                            { id: "kindergarten_teacher", label: "Νηπιαγωγός" },
                            { id: "primary_school_teacher", label: "Δημοτική Εκπαίδευση" },
                            { id: "pedagogy_general", label: "Παιδαγωγικά (Γενικά)" },
                            { id: "foreign_languages", label: "Ξένες Γλώσσες" },
                            { id: "sign_language", label: "Νοηματική Γλώσσα" },
                            { id: "braille_system", label: "Γραφή Braille" },
                            { id: "special_needs_education", label: "Ειδικής Αγωγής" },
                            { id: "occupational_therapy", label: "Εργοθεραπεία" },
                            { id: "speech_therapy", label: "Λογοθεραπεία" },
                            { id: "music_pedagogy", label: "Μουσικοπαιδαγωγικά" },
                            { id: "music_therapy", label: "Μουσικοθεραπεία" },
                            { id: "musical_instrument", label: "Μουσικό Όργανο" },
                            { id: "music_theory", label: "Θεωρητικά της Μουσικής" },
                            { id: "theatre_pedagogy", label: "Θεατροπαιδαγωγικά" },
                            { id: "drama_therapy", label: "Δραματοθεραπεία" },
                            { id: "play_therapy", label: "Παιγνιοθεραπεία" },
                            { id: "visual_arts", label: "Εικαστικές Τέχνες" },
                            { id: "dance_kinesiology", label: "Χορός-Κινησιολογία" },
                            { id: "dance_movement_therapy", label: "Χοροθεραπεία" },
                            { id: "psychology", label: "Ψυχολογία" },
                            { id: "child_psychology", label: "Παιδοψυχολογία" },
                            { id: "child_psychiatry", label: "Παιδοψυχιατρική" },
                            { id: "other", label: "Άλλο" },
                          ].find(option => option.id === typeId);

                          return (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {specializationOption?.label || typeId}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </Card>
      </div>

      {/* Profile Details */}
      <div className="space-y-6">
        {/* Specialization Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Briefcase className="h-4 w-4 text-primary" />
            <label className="text-sm font-medium">Ειδίκευση</label>
          </div>
          {editMode ? (
            <div className="flex flex-wrap gap-2">
              {positionOptions.map((position) => (
                <div key={position.id} className="flex items-center gap-2">
                  <Checkbox
                    id={`position-${position.id}`}
                    checked={
                      editedCandidate?.form_data?.position_interests?.includes(
                        position.id
                      ) || false
                    }
                    onCheckedChange={() =>
                      handlePositionInterestToggle(position.id)
                    }
                  />
                  <Label className="cursor-pointer">
                    {position.label.charAt(0).toUpperCase() +
                      position.label.slice(1).replace("_", " ")}
                  </Label>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {candidate.form_data.position_interests?.map(
                (position, index) => (
                  <Badge key={index} variant="secondary">
                    {position.charAt(0).toUpperCase() +
                      position.slice(1).replace("_", " ")}
                  </Badge>
                )
              )}
            </div>
          )}

          {/* Personal Information */}
          <Card className="p-4" variant="accent">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
              <div className="flex items-center gap-2">
                <User className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-medium text-primary">
                  Προσωπικές Πληροφορίες
                </h3>
              </div>
              <div className="space-y-4 md:col-span-2 lg:col-span-3">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Birth Date */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Ημερομηνία Γέννησης
                    </p>
                    {editMode ? (
                      <Input
                        value={editedCandidate?.form_data?.birth_date || ""}
                        onChange={(e) =>
                          handleInputChange(
                            "form_data.birth_date",
                            e.target.value
                          )
                        }
                        placeholder="DD/MM/YYYY"
                      />
                    ) : (
                      <p className="font-medium">
                        {candidate.form_data.birth_date}
                      </p>
                    )}
                  </div>

                  {/* Nationality */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Εθνικότητα</p>
                    {editMode ? (
                      <Input
                        value={editedCandidate?.form_data?.nationality || ""}
                        onChange={(e) =>
                          handleInputChange(
                            "form_data.nationality",
                            e.target.value
                          )
                        }
                        placeholder="Εθνικότητα"
                      />
                    ) : (
                      <p className="font-medium">
                        {candidate.form_data.nationality || "-"}
                      </p>
                    )}
                  </div>

                  {/* Languages */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Γλώσσες</p>
                    {editMode ? (
                      <div className="space-y-2">
                        {(editedCandidate?.form_data.languages || []).map(
                          (lang, index) => (
                            <div
                              key={index}
                              className="flex gap-2 items-center"
                            >
                              <Select
                                value={lang.language}
                                onValueChange={(value) =>
                                  handleLanguageChange(index, "language", value)
                                }
                              >
                                <SelectTrigger className="flex-1">
                                  <SelectValue placeholder="Γλώσσα" />
                                </SelectTrigger>
                                <SelectContent>
                                  {languageOptions.map((option) => (
                                    <SelectItem
                                      key={option.id}
                                      value={option.id}
                                    >
                                      {option.labelEl}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <Select
                                value={lang.level}
                                onValueChange={(value) =>
                                  handleLanguageChange(index, "level", value)
                                }
                              >
                                <SelectTrigger className="flex-1">
                                  <SelectValue placeholder="Επίπεδο" />
                                </SelectTrigger>
                                <SelectContent>
                                  {languageLevels.map((level) => (
                                    <SelectItem key={level.id} value={level.id}>
                                      {level.labelEl}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {index > 0 && (
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="icon"
                                  onClick={() => handleRemoveLanguage(index)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          )
                        )}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleAddLanguage}
                          className="mt-2 w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" /> Προσθήκη Γλώσσας
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {Array.isArray(candidate.form_data.languages) &&
                          candidate.form_data.languages.length > 0 ? (
                          candidate.form_data.languages.map(
                            (language, index) => (
                              <Badge key={index} variant="secondary">
                                {getLanguageLabel(language.language, "el")}
                              </Badge>
                            )
                          )
                        ) : (
                          <p className="text-gray-400">-</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Years of Experience */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Χρόνια Εμπειρίας
                    </p>
                    {editMode ? (
                      <Input
                        type="number"
                        value={
                          editedCandidate?.form_data.years_experience || ""
                        }
                        onChange={(e) =>
                          handleInputChange("years_experience", e.target.value)
                        }
                        placeholder="Χρόνια εμπειρίας"
                      />
                    ) : (
                      <p className="font-medium">
                        {candidate.form_data?.years_experience || "-"}
                      </p>
                    )}
                  </div>

                  {/* Duration */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Διάρκεια Συνεργασίας
                    </p>
                    {editMode ? (
                      <div className="flex flex-wrap gap-2">
                        {durationOptions.map((option) => (
                          <div
                            key={option.id}
                            className="flex items-center gap-2"
                          >
                            <Checkbox
                              id={`duration-${option.id}`}
                              checked={
                                editedCandidate?.form_data.duration_interests?.includes(
                                  option.id
                                ) || false
                              }
                              onCheckedChange={() => {
                                return handleDurationInterestToggle(option.id);
                              }}
                            />
                            <Label
                              htmlFor={`duration-${option.id}`}
                              className="cursor-pointer"
                            >
                              {option.label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {Array.isArray(
                          candidate.form_data.duration_interests
                        ) &&
                          candidate.form_data.duration_interests.length > 0 ? (
                          candidate.form_data.duration_interests.map(
                            (duration, index) => (
                              <Badge key={index} variant="secondary">
                                {duration
                                  .split("-")
                                  .map(
                                    (word: string) =>
                                      word[0].toUpperCase() + word.slice(1)
                                  )
                                  .join("-")}
                              </Badge>
                            )
                          )
                        ) : (
                          <p className="text-gray-400">-</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Schedule Interests */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Τύπος Εργασίας
                    </p>
                    {editMode ? (
                      <div className="flex flex-wrap gap-2">
                        {scheduleOptions.map((option) => (
                          <div
                            key={option.id}
                            className="flex items-center gap-2"
                          >
                            <Checkbox
                              id={`schedule-${option.id}`}
                              checked={
                                editedCandidate?.form_data.schedule_interests?.includes(
                                  option.id
                                ) || false
                              }
                              onCheckedChange={() =>
                                handleScheduleInterestToggle(option.id)
                              }
                            />
                            <Label
                              htmlFor={`schedule-${option.id}`}
                              className="cursor-pointer"
                            >
                              {option.label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {Array.isArray(
                          candidate.form_data.schedule_interests
                        ) &&
                          candidate.form_data.schedule_interests.length > 0 ? (
                          candidate.form_data.schedule_interests.map(
                            (schedule, index) => (
                              <Badge key={index} variant="secondary">
                                {schedule
                                  .split("-")
                                  .map(
                                    (word) =>
                                      word[0].toUpperCase() + word.slice(1)
                                  )
                                  .join("-")}
                              </Badge>
                            )
                          )
                        ) : (
                          <p className="text-gray-400">-</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Availability */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Διαθεσιμότητα
                    </p>
                    {editMode ? (
                      <Select
                        value={
                          editedCandidate?.form_data.start_availability || ""
                        }
                        onValueChange={(value) =>
                          handleInputChange(
                            "form_data.start_availability",
                            value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Επιλογή Διαθεσιμότητας" />
                        </SelectTrigger>
                        <SelectContent>
                          {startDateOptions.map((option) => (
                            <SelectItem key={option.id} value={option.id}>
                              {option.labelEl}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="font-medium">
                        {startDateOptions.find(
                          (option) =>
                            option.id === candidate.form_data.start_availability
                        )?.labelEl ||
                          candidate.form_data.start_availability ||
                          "-"}
                      </p>
                    )}
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Φύλο</p>
                    {editMode ? (
                      <Select
                        value={
                          editedCandidate?.form_data.gender.toLowerCase() || ""
                        }
                        onValueChange={(value) =>
                          handleInputChange("form_data.gender", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Επιλογή Φύλου" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Άνδρας</SelectItem>
                          <SelectItem value="female">Γυναίκα</SelectItem>
                          <SelectItem value="other">Άλλο</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="font-medium">
                        {candidate.form_data.gender.toLowerCase() === "male"
                          ? "Άνδρας"
                          : candidate.form_data.gender.toLowerCase() ===
                            "female"
                            ? "Γυναίκα"
                            : candidate.form_data.gender || "-"}
                      </p>
                    )}
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Διεύθυνση</p>
                    {editMode ? (
                      <Input
                        value={editedCandidate?.form_data?.address || ""}
                        onChange={(e) =>
                          handleInputChange("form_data.address", e.target.value)
                        }
                        placeholder="Διεύθυνση"
                      />
                    ) : (
                      <p className="font-medium">
                        {candidate.form_data.address || "-"}
                      </p>
                    )}
                  </div>

                  {/* Social Media */}
                  <div className="space-y-1 md:col-span-2 lg:col-span-3">
                    <p className="text-sm text-muted-foreground">
                      Social Media
                    </p>
                    {editMode ? (
                      <div className="space-y-3">
                        {(
                          editedCandidate?.form_data?.socialMedia || []
                        ).map((entry, index) => (
                          <div
                            key={index}
                            className="flex flex-col sm:flex-row gap-2 items-start p-3 border rounded"
                          >
                            <Select
                              value={entry.platform}
                              onValueChange={(value) =>
                                handleSocialMediaFieldChange(
                                  index,
                                  "platform",
                                  value
                                )
                              }
                            >
                              <SelectTrigger className="flex-1 min-w-[150px]">
                                <SelectValue placeholder="Platform" />
                              </SelectTrigger>
                              <SelectContent>
                                {socialMediaPlatformOptions.map((option) => (
                                  <SelectItem
                                    key={option.id}
                                    value={option.id}
                                  >
                                    {option.labelEl} / {option.labelEn}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Input
                              value={entry.handle}
                              onChange={(e) =>
                                handleSocialMediaFieldChange(
                                  index,
                                  "handle",
                                  e.target.value
                                )
                              }
                              placeholder="Handle or Link"
                              className="flex-1 min-w-[150px]"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="icon"
                              onClick={() => removeSocialMediaEntry(index)}
                              className="mt-2 sm:mt-0"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addSocialMediaEntry}
                          className="mt-2"
                        >
                          <Plus className="h-4 w-4 mr-2" /> Προσθήκη Social
                          Media
                        </Button>
                      </div>
                    ) : candidate.form_data?.socialMedia &&
                      candidate.form_data.socialMedia.length > 0 ? (
                      <div className="flex flex-col gap-1">
                        {candidate.form_data.socialMedia.map(
                          (sm, index) => (
                            <Badge key={index} variant="secondary">
                              {socialMediaPlatformOptions.find(
                                (p) => p.id === sm.platform
                              )?.labelEl || sm.platform}
                              : {sm.handle}
                            </Badge>
                          )
                        )}
                      </div>
                    ) : (
                      <p className="font-medium">-</p>
                    )}
                  </div>

                  {/* Allergies */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Αλλεργίες</p>
                    {editMode ? (
                      <div className="space-y-2">
                        <RadioGroup
                          value={
                            editedCandidate?.form_data?.allergies
                              ?.hasAllergies || "no"
                          }
                          onValueChange={(value) =>
                            handleAllergyChange("hasAllergies", value)
                          }
                          className="flex space-x-2"
                        >
                          <div className="flex items-center space-x-1">
                            <RadioGroupItem value="yes" id={`allergies-yes`} />
                            <Label htmlFor={`allergies-yes`} className="cursor-pointer">Ναι</Label>
                          </div>
                          <div className="flex items-center space-x-1">
                            <RadioGroupItem value="no" id={`allergies-no`} />
                            <Label htmlFor={`allergies-no`} className="cursor-pointer">Όχι</Label>
                          </div>
                        </RadioGroup>
                        {editedCandidate?.form_data?.allergies
                          ?.hasAllergies === "yes" && (
                            <Textarea
                              value={
                                editedCandidate?.form_data?.allergies
                                  ?.allergyDetails || ""
                              }
                              onChange={(e) =>
                                handleAllergyChange(
                                  "allergyDetails",
                                  e.target.value
                                )
                              }
                              placeholder="Λεπτομέρειες αλλεργιών"
                              className="min-h-[60px]"
                            />
                          )}
                      </div>
                    ) : (
                      <div>
                        <p className="font-medium">
                          {candidate.form_data?.allergies?.hasAllergies ===
                            "yes"
                            ? "Ναι"
                            : "Όχι"}
                        </p>
                        {candidate.form_data?.allergies?.hasAllergies ===
                          "yes" &&
                          candidate.form_data?.allergies?.allergyDetails && (
                            <p className="text-xs text-muted-foreground">
                              {candidate.form_data.allergies.allergyDetails}
                            </p>
                          )}
                      </div>
                    )}
                  </div>

                  {/* Passport Active */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Ενεργό Διαβατήριο
                    </p>
                    {editMode ? (
                      <RadioGroup
                        value={
                          editedCandidate?.form_data?.passportActive || "no"
                        }
                        onValueChange={(value) =>
                          handleInputChange("form_data.passportActive", value)
                        }
                        className="flex space-x-2"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="yes" id={`passport-yes`} />
                          <Label htmlFor={`passport-yes`} className="cursor-pointer">Ναι</Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="no" id={`passport-no`} />
                          <Label htmlFor={`passport-no`} className="cursor-pointer">Όχι</Label>
                        </div>
                      </RadioGroup>
                    ) : (
                      <p className="font-medium">
                        {candidate.form_data?.passportActive === "yes"
                          ? "Ναι"
                          : "Όχι"}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Personal Profile/Bio */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <BookOpen className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">Προφίλ</h3>
          </div>
          {editMode ? (
            <Textarea
              value={editedCandidate?.form_data?.personal_profile || ""}
              onChange={(e) =>
                handleInputChange("form_data.personal_profile", e.target.value)
              }
              placeholder="Περιγραφή προφίλ"
              className="min-h-32"
            />
          ) : candidate.form_data.personal_profile ? (
            <div className="bg-muted/20 p-4 rounded-lg">
              <p className="whitespace-pre-line">
                {candidate.form_data.personal_profile}
              </p>
            </div>
          ) : (
            <p className="text-gray-400">Δεν υπάρχει καταχωρημένο προφίλ</p>
          )}
        </Card>

        {/* Hobbies */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <Heart className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">
              Χόμπι & Ενδιαφέροντα
            </h3>
          </div>
          {editMode ? (
            <Textarea
              value={editedCandidate?.form_data?.hobbies || ""}
              onChange={(e) =>
                handleInputChange("form_data.hobbies", e.target.value)
              }
              placeholder="Χόμπι και ενδιαφέροντα"
              className="min-h-24"
            />
          ) : candidate.form_data.hobbies ? (
            <div className="bg-muted/20 p-4 rounded-lg">
              <p className="whitespace-pre-line">
                {candidate.form_data.hobbies}
              </p>
            </div>
          ) : (
            <p className="text-gray-400">Δεν υπάρχουν καταχωρημένα χόμπι</p>
          )}
        </Card>

        {/* Tutor-specific fields */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <Music className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">
              Πληροφορίες Υποψηφίου
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Candidate Type */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Τύπος</p>
              <p className="font-medium">
                {candidate.form_data?.candidate_type === "nanny"
                  ? "Νταντά"
                  : candidate.form_data?.candidate_type === "tutor"
                    ? "Δάσκαλος"
                    : candidate.form_data?.candidate_type === "both"
                      ? "Και τα δύο"
                      : "-"}
              </p>
            </div>

            {/* Level */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Επίπεδο</p>
              {editMode && !isNannyUser ? (
                <Select
                  value={editedCandidate?.form_data?.level || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.level", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή Επιπέδου" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="junior">Junior</SelectItem>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="vip">VIP</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {candidate.form_data?.level === "junior"
                    ? "Junior"
                    : candidate.form_data?.level === "basic"
                      ? "Basic"
                      : candidate.form_data?.level === "vip"
                        ? "VIP"
                        : "-"}
                </p>
              )}
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                Προτιμήσεις Περιοχής
              </p>
              {editMode ? (
                <Input
                  value={editedCandidate?.form_data?.place_preferences || ""}
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.place_preferences",
                      e.target.value
                    )
                  }
                  placeholder="Προτιμήσεις Περιοχής"
                />
              ) : (
                <p className="font-medium">
                  {candidate.form_data?.place_preferences || "-"}
                </p>
              )}
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                Πιστοποίηση Πρώτων Βοηθειών
              </p>
              {editMode ? (
                <div className="flex flex-col space-y-2 mt-2 border p-4 rounded-md">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="infants"
                      checked={
                        editedCandidate?.form_data?.first_aid_certification?.includes(
                          "infants"
                        ) || false
                      }
                      onCheckedChange={(checked) => {
                        const currentCertifications = Array.isArray(
                          editedCandidate?.form_data?.first_aid_certification
                        )
                          ? [
                            ...editedCandidate.form_data
                              .first_aid_certification,
                          ]
                          : [];

                        if (checked) {
                          if (!currentCertifications.includes("infants")) {
                            currentCertifications.push("infants");
                          }
                        } else {
                          const index =
                            currentCertifications.indexOf("infants");
                          if (index !== -1) {
                            currentCertifications.splice(index, 1);
                          }
                        }

                        handleInputChange(
                          "form_data.first_aid_certification",
                          currentCertifications
                        );
                      }}
                    />
                    <Label htmlFor="infants" className="cursor-pointer">
                      Για βρέφη
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="children"
                      checked={
                        editedCandidate?.form_data?.first_aid_certification?.includes(
                          "children"
                        ) || false
                      }
                      onCheckedChange={(checked) => {
                        const currentCertifications = Array.isArray(
                          editedCandidate?.form_data?.first_aid_certification
                        )
                          ? [
                            ...editedCandidate.form_data
                              .first_aid_certification,
                          ]
                          : [];

                        if (checked) {
                          if (!currentCertifications.includes("children")) {
                            currentCertifications.push("children");
                          }
                        } else {
                          const index =
                            currentCertifications.indexOf("children");
                          if (index !== -1) {
                            currentCertifications.splice(index, 1);
                          }
                        }

                        handleInputChange(
                          "form_data.first_aid_certification",
                          currentCertifications
                        );
                      }}
                    />
                    <Label htmlFor="children" className="cursor-pointer">
                      Για παιδιά
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="adults"
                      checked={
                        editedCandidate?.form_data?.first_aid_certification?.includes(
                          "adults"
                        ) || false
                      }
                      onCheckedChange={(checked) => {
                        const currentCertifications = Array.isArray(
                          editedCandidate?.form_data?.first_aid_certification
                        )
                          ? [
                            ...editedCandidate.form_data
                              .first_aid_certification,
                          ]
                          : [];

                        if (checked) {
                          if (!currentCertifications.includes("adults")) {
                            currentCertifications.push("adults");
                          }
                        } else {
                          const index = currentCertifications.indexOf("adults");
                          if (index !== -1) {
                            currentCertifications.splice(index, 1);
                          }
                        }

                        handleInputChange(
                          "form_data.first_aid_certification",
                          currentCertifications
                        );
                      }}
                    />
                    <Label htmlFor="adults" className="cursor-pointer">
                      Για ενήλικες
                    </Label>
                  </div>
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {Array.isArray(
                    candidate.form_data?.first_aid_certification
                  ) &&
                    candidate.form_data?.first_aid_certification.length > 0 ? (
                    candidate.form_data?.first_aid_certification.map(
                      (aidId, index) => {
                        const aid = firstAidOptions.find(
                          (option) => option.id === aidId
                        );
                        if (!aid) return null;
                        return (
                          <Badge key={index} variant="secondary">
                            {aid?.labelEl ||
                              (aidId === "adults" ? "Για ενήλικες" : aidId)}
                          </Badge>
                        );
                      }
                    )
                  ) : (
                    <p className="text-gray-400">-</p>
                  )}
                </div>
              )}
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                Τελευταία Ενημέρωση Πιστοποίησης Πρώτων Βοηθειών
              </p>
              {editMode ? (
                <Input
                  value={editedCandidate?.form_data?.first_aid_update || ""}
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.first_aid_update",
                      e.target.value
                    )
                  }
                  placeholder="Τελευταία Ενημέρωση Πιστοποίησης Πρώτων Βοηθειών"
                />
              ) : (
                <div className="flex flex-wrap gap-1">
                  <p className="font-medium">
                    {candidate.form_data?.first_aid_update || "-"}
                  </p>
                </div>
              )}
            </div>

            {/* Musical Instruments */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Μουσικά Όργανα</p>
              {editMode ? (
                <div className="flex flex-wrap gap-2">
                  {musicalInstrumentOptions.map((instrument) => (
                    <div
                      key={instrument.id}
                      className="flex items-center gap-2"
                    >
                      <Checkbox
                        id={`instrument-${instrument.id}`}
                        checked={
                          editedCandidate?.form_data?.musical_instruments?.includes(
                            instrument.id
                          ) || false
                        }
                        onCheckedChange={() =>
                          handleMusicalInstrumentsToggle(instrument.id)
                        }
                      />
                      <Label
                        htmlFor={`instrument-${instrument.id}`}
                        className="cursor-pointer"
                      >
                        {instrument.label.split("/")[0]}
                      </Label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {Array.isArray(candidate.form_data?.musical_instruments) &&
                    candidate.form_data?.musical_instruments.length > 0 ? (
                    candidate.form_data?.musical_instruments.map(
                      (instrumentId, index) => {
                        const instrument = musicalInstrumentOptions.find(
                          (option) => option.id === instrumentId
                        );
                        return (
                          <Badge key={index} variant="secondary">
                            {instrument
                              ? instrument.label.split("/")[0]
                              : instrumentId}
                          </Badge>
                        );
                      }
                    )
                  ) : (
                    <p className="text-gray-400">-</p>
                  )}
                </div>
              )}
            </div>

            {/* Music Theory */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Θεωρία Μουσικής</p>
              {editMode ? (
                <div className="flex flex-wrap gap-2">
                  {musicTheoryOptions.map((theory) => (
                    <div key={theory.id} className="flex items-center gap-2">
                      <Checkbox
                        id={`theory-${theory.id}`}
                        checked={
                          editedCandidate?.form_data?.music_theory?.includes(
                            theory.id
                          ) || false
                        }
                        onCheckedChange={() =>
                          handleMusicTheoryToggle(theory.id)
                        }
                      />
                      <Label
                        htmlFor={`theory-${theory.id}`}
                        className="cursor-pointer"
                      >
                        {theory.label.split("/")[0]}
                      </Label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {Array.isArray(candidate.form_data?.music_theory) &&
                    candidate.form_data?.music_theory.length > 0 ? (
                    candidate.form_data?.music_theory.map((theoryId, index) => {
                      const theory = musicTheoryOptions.find(
                        (option) => option.id === theoryId
                      );
                      return (
                        <Badge key={index} variant="secondary">
                          {theory ? theory.label.split("/")[0] : theoryId}
                        </Badge>
                      );
                    })
                  ) : (
                    <p className="text-gray-400">-</p>
                  )}
                </div>
              )}
            </div>

            {/* Lesson Format */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Μορφή Μαθημάτων</p>
              {editMode ? (
                <div className="flex flex-wrap gap-2">
                  {lessonFormatOptions.map((format) => (
                    <div key={format.id} className="flex items-center gap-2">
                      <Checkbox
                        id={`format-${format.id}`}
                        checked={
                          editedCandidate?.form_data?.lesson_format?.includes(
                            format.id
                          ) || false
                        }
                        onCheckedChange={() =>
                          handleLessonFormatToggle(format.id)
                        }
                      />
                      <Label
                        htmlFor={`format-${format.id}`}
                        className="cursor-pointer"
                      >
                        {format.label.split("/")[0]}
                      </Label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {Array.isArray(candidate.form_data?.lesson_format) &&
                    candidate.form_data?.lesson_format.length > 0 ? (
                    candidate.form_data?.lesson_format.map(
                      (formatId, index) => {
                        const format = lessonFormatOptions.find(
                          (option) => option.id === formatId
                        );
                        return (
                          <Badge key={index} variant="secondary">
                            {format ? format.label.split("/")[0] : formatId}
                          </Badge>
                        );
                      }
                    )
                  ) : (
                    <p className="text-gray-400">-</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ProfileTab;
