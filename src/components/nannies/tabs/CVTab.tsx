import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import apiClient from "@/lib/api";
import { childrenAgeOptions, educationOptions } from "@/schemas/FormSchema";
import { Download, Eye, Upload, X } from "lucide-react";
import { Candidate } from "../models/Candidate";
import CVPreview from "./CVPreview";

interface CVTabProps {
  candidate: Candidate;
  editedCandidate: Candidate | null;
  editMode: boolean;
  handleInputChange: (
    field: string,
    value: string | boolean | string[]
  ) => void;
  handleFileUpload?: (file: File, docType: string) => Promise<void>;
  handleFilePreview: (url: string) => void;
  handleFileDownload: (filePath: string) => void;
  getFilePreviewUrl: (
    folder: string,
    filePath: string,
    expiresIn?: number
  ) => Promise<string>;
  // Additional props for file handling
  triggerFileInput?: (ref: React.RefObject<HTMLInputElement>) => void;
  fileInputRefs?: Record<string, React.RefObject<HTMLInputElement>>;
  cvFile?: File | null;
  isNannyUser?: boolean;
}

const CVTab = ({
  candidate,
  editedCandidate,
  editMode,
  handleInputChange,
  handleFilePreview,
  handleFileDownload,
  getFilePreviewUrl,
  triggerFileInput,
  fileInputRefs,
  cvFile,
  isNannyUser,
}: CVTabProps) => {
  return (
    <div className="space-y-6">
      {/* CV Upload/Preview */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold mb-4">Βιογραφικό</h3>
          {!isNannyUser && (
            <Button
              onClick={async () => {
                try {
                  await apiClient.put(
                    `/candidates/${candidate.id}/generate-file`,
                    {
                      file_type: "cv",
                    }
                  );
                  toast({
                    title: "Επιτυχία",
                    description: "Το Βιογραφικό δημιουργήθηκε με επιτυχία.",
                  });
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                } catch (error) {
                  console.error("Error generating CV:", error);
                  toast({
                    title: "Σφάλμα",
                    description: "Απέτυχε η δημιουργία του Βιογραφικού.",
                    variant: "destructive",
                  });
                }
              }}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/10"
            >
              Δημιουργία Βιογραφικού
            </Button>
          )}
        </div>

        {candidate.cv_generated && !editMode ? (
          <div className="w-full">
            <CVPreview
              cvUrl={`${candidate.storage_id}/cv.pdf`}
              getFilePreviewUrl={getFilePreviewUrl}
              expiresIn={14400} // 4 hours
            />
          </div>
        ) : (
          <div className="space-y-4">
            {editedCandidate?.cv_generated ? (
              <div className="flex items-center justify-between p-3 border rounded-md">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">PDF</Badge>
                  <span className="text-sm">
                    {editedCandidate.form_data.cv?.split("/").pop() || "cv.pdf"}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() =>
                      handleFilePreview(`${editedCandidate.storage_id}/cv.pdf`)
                    }
                  >
                    <Eye size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() =>
                      handleFileDownload(`${editedCandidate.storage_id}/cv.pdf`)
                    }
                  >
                    <Download size={16} />
                  </Button>
                </div>
              </div>
            ) : editMode ? (
              <div className="flex flex-col space-y-4">
                <div className="flex items-center justify-center p-6 border-2 border-dashed rounded-md">
                  <div
                    onClick={() =>
                      fileInputRefs?.cv?.current &&
                      triggerFileInput?.(fileInputRefs.cv)
                    }
                    className="flex flex-col items-center cursor-pointer"
                  >
                    <Upload size={24} className="mb-2" />
                    <span className="text-sm font-medium">
                      Ανεβάστε το βιογραφικό σας
                    </span>
                    <span className="text-xs text-muted-foreground">
                      PDF, DOC, DOCX (max 5MB)
                    </span>
                  </div>
                </div>

                {cvFile && (
                  <div className="flex items-center justify-between p-3 border rounded-md bg-muted/10">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        {cvFile.name.split(".").pop()?.toUpperCase() || "FILE"}
                      </Badge>
                      <span className="text-sm">{cvFile.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(cvFile.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <p>Δεν έχει ανέβει βιογραφικό</p>
            )}
          </div>
        )}
      </Card>

      {/* Education */}
      <div className="mt-6 space-y-4">
        <h4 className="text-md font-medium">Εκπαίδευση</h4>
        {editMode ? (
          <div className="space-y-4">
            {editedCandidate?.form_data?.education &&
              Array.isArray(editedCandidate.form_data.education) &&
              editedCandidate.form_data.education.length > 0 ? (
              editedCandidate.form_data.education.map((edu, index) => (
                <div key={index} className="border p-4 rounded-lg space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <select
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={edu || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        // Create a deep copy of the education array
                        const updatedEducation = [
                          ...(editedCandidate.form_data.education || []),
                        ];
                        updatedEducation[index] = e.target.value;

                        // Update the form data
                        handleInputChange(
                          "form_data.education",
                          updatedEducation
                        );
                      }}
                    >
                      <option value="">Επιλέξτε επίπεδο εκπαίδευσης</option>
                      {educationOptions.map((option) => (
                        <option key={option.id} value={option.id}>
                          {option.label.split("/")[0]}
                        </option>
                      ))}
                    </select>
                    <Input
                      value={
                        editedCandidate.form_data.education_titles &&
                          Array.isArray(
                            editedCandidate.form_data.education_titles
                          ) &&
                          index <
                          editedCandidate.form_data.education_titles.length
                          ? editedCandidate.form_data.education_titles[index] ||
                          ""
                          : ""
                      }
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        // Create a deep copy of the education_titles array
                        const updatedTitles = [
                          ...(editedCandidate.form_data.education_titles || []),
                        ];

                        // Ensure the array is long enough
                        while (updatedTitles.length <= index) {
                          updatedTitles.push("");
                        }

                        // Update the value
                        updatedTitles[index] = e.target.value;

                        // Update the form data
                        handleInputChange(
                          "form_data.education_titles",
                          updatedTitles
                        );
                      }}
                      placeholder="Τίτλος σπουδών"
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      if (!editedCandidate) return;

                      // Create a deep copy of the arrays
                      const updatedEducation = [
                        ...(editedCandidate.form_data.education || []),
                      ];
                      const updatedTitles = [
                        ...(editedCandidate.form_data.education_titles || []),
                      ];

                      // Remove the items at the specified index
                      updatedEducation.splice(index, 1);
                      if (index < updatedTitles.length) {
                        updatedTitles.splice(index, 1);
                      }

                      // Update the form data
                      handleInputChange(
                        "form_data.education",
                        updatedEducation
                      );
                      handleInputChange(
                        "form_data.education_titles",
                        updatedTitles
                      );
                    }}
                  >
                    Αφαίρεση
                  </Button>
                </div>
              ))
            ) : (
              <p>Δεν έχει δηλωθεί εκπαίδευση</p>
            )}
            <Button
              variant="outline"
              onClick={() => {
                if (!editedCandidate) return;

                // Create a deep copy of the arrays
                const updatedEducation = [
                  ...(editedCandidate.form_data.education || []),
                ];
                const updatedTitles = [
                  ...(editedCandidate.form_data.education_titles || []),
                ];

                // Add new items
                updatedEducation.push(educationOptions[0].id);
                updatedTitles.push("");

                // Update the form data
                handleInputChange("form_data.education", updatedEducation);
                handleInputChange("form_data.education_titles", updatedTitles);
              }}
            >
              Προσθήκη Εκπαίδευσης
            </Button>
          </div>
        ) : Array.isArray(candidate.form_data.education) &&
          candidate.form_data.education.length > 0 ? (
          <div className="space-y-3">
            {candidate.form_data.education.map((edu, index) => {
              const educationOption = educationOptions.find(
                (o) => o.id === edu
              );
              return (
                <div
                  key={index}
                  className="border-l-2 border-primary pl-4 py-2"
                >
                  <p className="font-medium">
                    {educationOption?.label.split("/")[0] || edu}
                  </p>
                  {candidate.form_data.education_titles &&
                    Array.isArray(candidate.form_data.education_titles) &&
                    index < candidate.form_data.education_titles.length &&
                    candidate.form_data.education_titles[index] && (
                      <p className="text-sm text-muted-foreground">
                        {candidate.form_data.education_titles[index]}
                      </p>
                    )}
                </div>
              );
            })}
          </div>
        ) : (
          <p>Δεν έχει δηλωθεί εκπαίδευση</p>
        )}
      </div>

      {/* Work Experience */}
      <div className="mt-6 space-y-4">
        <h4 className="text-md font-medium">Επαγγελματική Εμπειρία</h4>
        {editMode ? (
          <div className="space-y-4">
            {editedCandidate?.form_data?.work_experience ? (
              editedCandidate.form_data.work_experience.map((exp, index) => (
                <div key={index} className="border p-4 rounded-lg space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <Input
                      value={exp.position || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const experiences = [
                          ...(editedCandidate.form_data.work_experience || []),
                        ];
                        experiences[index] = {
                          ...experiences[index],
                          position: e.target.value,
                        };

                        handleInputChange(
                          "form_data.work_experience",
                          experiences as any
                        );
                      }}
                      placeholder="Θέση"
                    />
                    <Input
                      value={exp.location || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const experiences = [
                          ...(editedCandidate.form_data.work_experience || []),
                        ];
                        experiences[index] = {
                          ...experiences[index],
                          location: e.target.value,
                        };

                        handleInputChange(
                          "form_data.work_experience",
                          experiences as any
                        );
                      }}
                      placeholder="Εργοδότης"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <Input
                      value={exp.period || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const experiences = [
                          ...(editedCandidate.form_data.work_experience || []),
                        ];
                        experiences[index] = {
                          ...experiences[index],
                          period: e.target.value,
                        };

                        handleInputChange(
                          "form_data.work_experience",
                          experiences as any
                        );
                      }}
                      placeholder="Ημερομηνία έναρξης"
                    />
                    <Input
                      value={exp.children || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const experiences = [
                          ...(editedCandidate.form_data.work_experience || []),
                        ];
                        experiences[index] = {
                          ...experiences[index],
                          children: e.target.value,
                        };

                        handleInputChange(
                          "form_data.work_experience",
                          experiences as any
                        );
                      }}
                      placeholder="Ημερομηνία λήξης (αφήστε κενό για 'έως σήμερα')"
                    />
                  </div>
                  <Textarea
                    value={exp.endingReason || ""}
                    onChange={(e) => {
                      if (!editedCandidate) return;

                      const experiences = [
                        ...(editedCandidate.form_data.work_experience || []),
                      ];
                      experiences[index] = {
                        ...experiences[index],
                        endingReason: e.target.value,
                      };

                      handleInputChange(
                        "form_data.work_experience",
                        experiences as any
                      );
                    }}
                    placeholder="Περιγραφή"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      if (!editedCandidate) return;

                      const experiences = [
                        ...(editedCandidate.form_data.work_experience || []),
                      ];
                      experiences.splice(index, 1);

                      handleInputChange(
                        "form_data.work_experience",
                        experiences as any
                      );
                    }}
                  >
                    Αφαίρεση
                  </Button>
                </div>
              ))
            ) : (
              <p>Δεν έχει δηλωθεί επαγγελματική εμπειρία</p>
            )}
            <Button
              variant="outline"
              onClick={() => {
                if (!editedCandidate) return;

                const experiences = [
                  ...(editedCandidate.form_data.work_experience || []),
                  {
                    period: "",
                    location: "",
                    position: "",
                    children: "",
                    schedule: "",
                    endingReason: "",
                  },
                ];

                handleInputChange(
                  "form_data.work_experience",
                  experiences as any
                );
              }}
            >
              Προσθήκη Εμπειρίας
            </Button>
          </div>
        ) : Array.isArray(candidate.form_data.work_experience) &&
          candidate.form_data.work_experience.length > 0 ? (
          <div className="space-y-3">
            {candidate.form_data.work_experience.map((exp, index) => (
              <div key={index} className="border-l-2 border-primary pl-4 py-2">
                <p className="font-medium">{exp.position}</p>
                <p className="text-sm">{exp.location}</p>
                <p className="text-sm text-muted-foreground">{exp.period}</p>
                {exp.endingReason && (
                  <p className="text-sm mt-2">{exp.endingReason}</p>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p>Δεν έχει δηλωθεί επαγγελματική εμπειρία</p>
        )}
      </div>

      {/* Special Experience */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <h4 className="text-md font-medium">Εμπειρία με Παιδιά</h4>
          {editMode ? (
            <RadioGroup
              value={editedCandidate?.form_data?.experience_with_children || ""}
              onValueChange={(value) =>
                handleInputChange("form_data.experience_with_children", value)
              }
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="children-yes" />
                <Label htmlFor="children-yes">Ναι</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="children-no" />
                <Label htmlFor="children-no">Όχι</Label>
              </div>
            </RadioGroup>
          ) : (
            <p>
              {candidate.form_data.experience_with_children === "yes"
                ? "Ναι"
                : candidate.form_data.experience_with_children === "no"
                  ? "Όχι"
                  : candidate.form_data.experience_with_children ||
                  "Δεν έχει δηλωθεί"}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <p className="text-sm text-muted-foreground font-medium">
            Ηλικίες Παιδιών:
          </p>
          {editMode ? (
            <div className="grid grid-cols-2 gap-2 mt-2">
              {childrenAgeOptions.map((option) => (
                <div
                  key={option.id}
                  className="flex items-center space-x-2"
                >
                  <Checkbox
                    id={`checkbox-${option.id}`}
                    checked={
                      editedCandidate?.form_data?.children_age_experience?.includes(
                        option.id
                      ) || false
                    }
                    onCheckedChange={(checked) => {
                      const updatedValue = checked
                        ? [
                          ...(editedCandidate?.form_data?.children_age_experience ||
                            []),
                          option.id,
                        ]
                        : (
                          editedCandidate?.form_data?.children_age_experience || []
                        ).filter((value) => value !== option.id);
                      handleInputChange(
                        "form_data.children_age_experience",
                        updatedValue
                      );
                    }}
                  />
                  <Label
                    htmlFor={`checkbox-${option.id}`}
                    className="font-normal cursor-pointer"
                  >
                    {option.labelEl}
                  </Label>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-wrap gap-2 mt-2">
              {(() => {
                // Make sure childrenAgeOptions is properly loaded
                if (!childrenAgeOptions || childrenAgeOptions.length === 0) {
                  console.error("Children age options not loaded properly!");
                  return <p className="text-red-500">-</p>;
                }

                // Check if children_age exists and is an array
                if (
                  Array.isArray(candidate.form_data?.children_age_experience) &&
                  candidate.form_data?.children_age_experience.length > 0
                ) {
                  // Create a local copy to avoid any reference issues
                  const childrenAges = [
                    ...candidate.form_data.children_age_experience,
                  ];

                  return childrenAges.map((ageId) => {
                    // Find the matching option
                    const option = childrenAgeOptions.find(
                      (o) => o.id === ageId
                    );

                    // If option is found, display the badge with the label
                    if (option) {
                      return (
                        <Badge
                          key={ageId}
                          variant="secondary"
                          className="px-3 py-1 text-sm mb-2"
                        >
                          {option.labelEl}
                        </Badge>
                      );
                    }
                  });
                } else {
                  return <p className="text-gray-400">-</p>;
                }
              })()}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <h4 className="text-md font-medium">Εμπειρία με Ειδικές Ανάγκες</h4>
          {editMode ? (
            <RadioGroup
              value={editedCandidate?.form_data?.experience_special_needs || ""}
              onValueChange={(value) =>
                handleInputChange("form_data.experience_special_needs", value)
              }
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="special-needs-yes" />
                <Label htmlFor="special-needs-yes">Ναι</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="special-needs-no" />
                <Label htmlFor="special-needs-no">Όχι</Label>
              </div>
            </RadioGroup>
          ) : (
            <p>
              {candidate.form_data.experience_special_needs === "yes"
                ? "Ναι"
                : candidate.form_data.experience_special_needs === "no"
                  ? "Όχι"
                  : candidate.form_data.experience_special_needs ||
                  "Δεν έχει δηλωθεί"}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <h4 className="text-md font-medium">
            Εμπειρία με Μαθησιακές Δυσκολίες
          </h4>
          {editMode ? (
            <RadioGroup
              value={
                editedCandidate?.form_data?.experience_learning_difficulties ||
                ""
              }
              onValueChange={(value) =>
                handleInputChange(
                  "form_data.experience_learning_difficulties",
                  value
                )
              }
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="learning-difficulties-yes" />
                <Label htmlFor="learning-difficulties-yes">Ναι</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="learning-difficulties-no" />
                <Label htmlFor="learning-difficulties-no">Όχι</Label>
              </div>
            </RadioGroup>
          ) : (
            <p>
              {candidate.form_data.experience_learning_difficulties === "yes"
                ? "Ναι"
                : candidate.form_data.experience_learning_difficulties === "no"
                  ? "Όχι"
                  : candidate.form_data.experience_learning_difficulties ||
                  "Δεν έχει δηλωθεί"}
            </p>
          )}
        </div>
      </div>

      {/* References */}
      <div className="mt-6 space-y-4">
        <h4 className="text-md font-medium">Συστάσεις</h4>
        {editMode ? (
          <div className="space-y-3">
            <RadioGroup
              value={editedCandidate?.form_data?.references || ""}
              onValueChange={(value) =>
                handleInputChange("form_data.references", value)
              }
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="references-yes" />
                <Label htmlFor="references-yes">Ναι</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="references-no" />
                <Label htmlFor="references-no">Όχι</Label>
              </div>
            </RadioGroup>

            <h5 className="text-sm font-medium mt-4">Επαφές Συστάσεων</h5>
            {editedCandidate?.form_data?.references_contacts
              ? editedCandidate.form_data.references_contacts.map(
                (ref, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-1 md:grid-cols-2 gap-2 border p-3 rounded-lg"
                  >
                    <Input
                      value={ref.name || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const contacts = [
                          ...(editedCandidate.form_data.references_contacts ||
                            []),
                        ];
                        contacts[index] = {
                          ...contacts[index],
                          name: e.target.value,
                        };

                        handleInputChange(
                          "form_data.references_contacts",
                          contacts
                        );
                      }}
                      placeholder="Όνομα"
                    />
                    <Input
                      value={ref.position || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const contacts = [
                          ...(editedCandidate.form_data.references_contacts ||
                            []),
                        ];
                        contacts[index] = {
                          ...contacts[index],
                          position: e.target.value,
                        };

                        handleInputChange(
                          "form_data.references_contacts",
                          contacts
                        );
                      }}
                      placeholder="Θέση"
                    />
                    <Input
                      value={ref.phone || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const contacts = [
                          ...(editedCandidate.form_data.references_contacts ||
                            []),
                        ];
                        contacts[index] = {
                          ...contacts[index],
                          phone: e.target.value,
                        };

                        handleInputChange(
                          "form_data.references_contacts",
                          contacts
                        );
                      }}
                      type="tel"
                      placeholder="Τηλέφωνο"
                    />
                    <Input
                      value={ref.email || ""}
                      onChange={(e) => {
                        if (!editedCandidate) return;

                        const contacts = [
                          ...(editedCandidate.form_data.references_contacts ||
                            []),
                        ];
                        contacts[index] = {
                          ...contacts[index],
                          email: e.target.value,
                        };

                        handleInputChange(
                          "form_data.references_contacts",
                          contacts
                        );
                      }}
                      type="email"
                      placeholder="Email"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        if (!editedCandidate) return;

                        const contacts = [
                          ...(editedCandidate.form_data.references_contacts ||
                            []),
                        ];
                        contacts.splice(index, 1);

                        handleInputChange(
                          "form_data.references_contacts",
                          contacts
                        );
                      }}
                      className="h-10 w-10 mt-auto"
                    >
                      <X size={16} />
                    </Button>
                  </div>
                )
              )
              : null}
            <div className="flex gap-2 mt-2">
              <Button
                variant="outline"
                onClick={() => {
                  if (!editedCandidate) return;

                  const contacts = [
                    ...(editedCandidate.form_data.references_contacts || []),
                    {
                      name: "",
                      position: "",
                      email: "",
                      phone: "",
                    },
                  ];

                  handleInputChange("form_data.references_contacts", contacts);
                }}
              >
                Προσθήκη Επαφής
              </Button>
            </div>
          </div>
        ) : (
          <>
            {candidate.form_data.references ? (
              <div className="bg-muted/20 p-4 rounded-lg">
                <p className="whitespace-pre-line">
                  {candidate.form_data.references === "yes"
                    ? "Ναι"
                    : candidate.form_data.references === "no"
                      ? "Όχι"
                      : candidate.form_data.references}
                </p>
              </div>
            ) : (
              <p>Δεν έχουν δηλωθεί συστάσεις</p>
            )}

            {Array.isArray(candidate.form_data.references_contacts) &&
              candidate.form_data.references_contacts.length > 0 && (
                <div className="mt-2 space-y-3">
                  <h5 className="text-sm font-medium">Επαφές Συστάσεων</h5>
                  {candidate.form_data.references_contacts.map((ref, index) => (
                    <div key={index} className="border p-3 rounded-lg">
                      <p className="font-medium">{ref.name}</p>
                      <p className="text-sm">{ref.position}</p>
                      <p className="text-sm text-muted-foreground">
                        {ref.phone}
                      </p>
                      <p className="text-sm mt-2">{ref.email}</p>
                    </div>
                  ))}
                </div>
              )}
          </>
        )}
      </div>
    </div>
  );
};

export default CVTab;
