import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import apiClient from "@/lib/api";
import { supabase } from "@/lib/supabase";
import { getFilePreviewUrl } from "@/lib/utils";
import {
  fetchCandidateById,
  updateCandidate,
} from "@/services/candidateService";
import {
  DealStatus,
  fetchDeals,
  Deal as ServiceDeal,
} from "@/services/dealService";
import { PERMISSION_CODES } from "@/services/userService";
import {
  ArrowLeft,
  Briefcase,
  FileSpreadsheet,
  FileText,
  X,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { DateRange } from "react-day-picker"; // Assuming you'll use react-day-picker in ProfileTab
import { useNavigate, useParams } from "react-router-dom";
import ProfileHeader from "../common/ProfileHeader";
import CompactTimeline from "../deals/CompactTimeline";
import { Badge } from "../ui/badge";
import { Candidate } from "./models/Candidate";
import CVTab from "./tabs/CVTab";
import CommentsTab from "./tabs/CommentsTab";
import FilesTab from "./tabs/FilesTab";
import PriceListTab from "./tabs/PriceListTab";
import ProfileTab from "./tabs/ProfileTab";

// Use the Deal interface from the service
type Deal = ServiceDeal;

interface NannyProfileProps {
  isNannyUser?: boolean;
}

const NannyProfile = ({ isNannyUser = false }: NannyProfileProps) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { canEdit, canCreate, hasSpecificPermission } = usePermissionCheck();

  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [dealsLoading, setDealsLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editedCandidate, setEditedCandidate] = useState<Candidate | null>(
    null
  );

  // Hint states
  const [showCVHint, setShowCVHint] = useState(false);
  const [showPriceListHint, setShowPriceListHint] = useState(false);

  // File upload states
  const [cvFile, setCvFile] = useState<File | null>(null);
  const [docFiles, setDocFiles] = useState<Record<string, File | null>>({
    id_passport: null,
    criminal_record_document: null,
    driving_license_document: null,
    first_aid: null,
    intro_video: null,
    profile_photo: null,
    education_documents: null,
    reference_letters: null,
    cv: null,
    nanny_cv: null,
    price_list_file: null,
    client_cv_file: null,
  });

  const fileInputRefs = {
    cv: useRef<HTMLInputElement>(null),
    id_passport: useRef<HTMLInputElement>(null),
    criminal_record_document: useRef<HTMLInputElement>(null),
    driving_license_document: useRef<HTMLInputElement>(null),
    first_aid: useRef<HTMLInputElement>(null),
    intro_video: useRef<HTMLInputElement>(null),
    profile_photo: useRef<HTMLInputElement>(null),
    education_documents: useRef<HTMLInputElement>(null),
    reference_letters: useRef<HTMLInputElement>(null),
    nanny_cv: useRef<HTMLInputElement>(null),
    price_list_file: useRef<HTMLInputElement>(null),
    client_cv_file: useRef<HTMLInputElement>(null),
  };

  // Function to fetch deals by candidate ID
  const fetchCandidateDeals = async (candidateId: string) => {
    try {
      setDealsLoading(true);

      // Fetch deals with candidate_id filter using the updated service with joins
      const candidateDeals = await fetchDeals(false, {
        page: 1,
        itemsPerPage: 100,
        filters: { candidate_id: candidateId },
      });

      // Check if the result is paginated or an array
      if (Array.isArray(candidateDeals)) {
        setDeals(candidateDeals);
      } else {
        setDeals(candidateDeals.data);
      }
    } catch (error) {
      console.error("Error fetching deals:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία λήψης συμφωνιών υποψήφου.",
        variant: "destructive",
      });
    } finally {
      setDealsLoading(false);
    }
  };

  // Fetch candidate data
  const loadCandidate = async () => {
    setLoading(true);
    try {
      if (!id) return;

      const candidateData = await fetchCandidateById(id);

      if (!candidateData) {
        throw new Error("Candidate not found");
      }

      setCandidate(candidateData);
      setEditedCandidate(JSON.parse(JSON.stringify(candidateData))); // Deep copy

      // Fetch deals for this candidate
      await fetchCandidateDeals(candidateData.id.toString());
    } catch (error) {
      console.error("Error fetching candidate:", error);
      toast({
        title: "Υπήρξε πρόβλημα",
        description: "Υπήρξε πρόβλημα κατά την φόρτωση των δεδομένων.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadCandidate();
    }
  }, [id]);

  const handleEditCandidate = () => {
    // Check if user has permission to edit based on candidate type
    const candidateType = candidate?.form_data?.candidate_type;
    const isNanny = candidate?.is_nanny_approved;
    const isTutor = candidate?.is_tutor_approved;

    let hasEditPermission = false;

    if (isNanny) {
      hasEditPermission = canEdit("nannies");
    } else if (isTutor) {
      hasEditPermission = canEdit("tutors");
    } else {
      hasEditPermission = canEdit("candidates");
    }

    if (!hasEditPermission && !isNannyUser) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα επεξεργασίας αυτού του προφίλ.",
        variant: "destructive",
      });
      return;
    }

    setEditMode(true);
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    setEditedCandidate(JSON.parse(JSON.stringify(candidate))); // Reset to original
    setCvFile(null);
    setDocFiles({
      id_passport: null,
      criminal_record_document: null,
      driving_license_document: null,
      first_aid: null,
      insurance: null,
      intro_video: null,
      work_documents: null,
      profile_photo: null,
      education_documents: null,
      reference_letters: null,
      cv: null,
      nanny_cv: null,
      price_list_file: null,
      // Note: editedCandidate.form_data.availability is reset by JSON.parse(JSON.stringify(candidate))
    });
  };

  const handleInputChange = (
    field: string,
    value: string | boolean | string[] | any
  ) => {
    if (!editedCandidate) return;

    // Handle nested form_data fields
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      if (parent === "form_data") {
        // Create a deep copy of the form_data
        const updatedFormData = {
          ...editedCandidate.form_data,
          [child]: value,
        };

        // Update the candidate with the new form_data
        const updatedCandidate = {
          ...editedCandidate,
          form_data: updatedFormData,
        };

        setEditedCandidate(updatedCandidate);
      }
    } else {
      // Handle top-level fields
      setEditedCandidate({
        ...editedCandidate,
        [field]: value,
      });
    }
  };

  const handlePositionInterestToggle = (positionId: string) => {
    if (!editedCandidate) return;

    const currentInterests = [
      ...(editedCandidate.form_data.position_interests || []),
    ];
    const index = currentInterests.indexOf(positionId);

    if (index === -1) {
      // Add interest
      currentInterests.push(positionId);
    } else {
      // Remove interest
      currentInterests.splice(index, 1);
    }

    // Update the position_interests in form_data
    setEditedCandidate({
      ...editedCandidate,
      form_data: {
        ...editedCandidate.form_data,
        position_interests: currentInterests,
      },
    });
  };
  const handleFileChange = (
    type: "cv" | keyof typeof docFiles,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files && e.target.files[0]) {
      if (type === "cv") {
        setCvFile(e.target.files[0]);
        toast({
          title: "Επιλέχθηκε αρχείο",
          description: `Το αρχείο CV "${e.target.files[0].name}" επιλέχθηκε για ανέβασμα.`,
        });
      } else {
        setDocFiles({
          ...docFiles,
          [type]: e.target.files[0],
        });
        toast({
          title: "Επιλέχθηκε αρχείο",
          description: `Το έγγραφο "${e.target.files[0].name}" επιλέχθηκε για ανέβασμα.`,
        });
      }
    }
  };

  const triggerFileInput = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.click();
    }
  };

  const handleFilePreview = async (url: string) => {
    try {
      // If it's a path and not a full URL, get a signed URL
      if (!url.startsWith("http")) {
        // Use a longer expiration time for preview (4 hours)
        const expiresIn = 14400; // 4 hours in seconds

        // Use our utility function to get a properly formatted URL
        const previewUrl = await getFilePreviewUrl(
          "candidate-documents",
          url,
          expiresIn
        );

        // Open the URL in a new tab
        window.open(previewUrl, "_blank");
      } else {
        // If it's already a URL, just open it
        window.open(url, "_blank");
      }
    } catch (error) {
      console.error("Error previewing file:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία προβολής αρχείου. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  const handleFileDownload = async (filePath: string) => {
    try {
      let downloadUrl = filePath;

      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use a longer expiration time for downloads (24 hours)
        const { data, error } = await supabase.storage
          .from("candidate-documents")
          .createSignedUrl(filePath, 86400); // 24 hours in seconds

        if (error) {
          throw error;
        }

        downloadUrl = data.signedUrl;
      }

      // Create a temporary link element to trigger the download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filePath.split("/").pop() || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Λήψη Ξεκίνησε",
        description: "Η λήψη του αρχείου σας ξεκίνησε.",
      });
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία λήψης αρχείου. Ο σύνδεσμος ενδέχεται να έχει λήξει - παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  const handleSaveChanges = async () => {
    if (!editedCandidate) return;

    // Check if user has permission to edit based on candidate type
    const isNanny = candidate?.is_nanny_approved;
    const isTutor = candidate?.is_tutor_approved;

    let hasEditPermission = false;

    if (isNanny) {
      hasEditPermission = canEdit("nannies");
    } else if (isTutor) {
      hasEditPermission = canEdit("tutors");
    } else {
      hasEditPermission = canEdit("candidates");
    }

    if (!hasEditPermission && !isNannyUser) {
      toast({
        title: "Σφάλμα",
        description:
          "Δεν έχετε δικαίωμα αποθήκευσης αλλαγών σε αυτό το προφίλ.",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);

    try {
      // First save the candidate data using our service
      // This updateCandidate service call should now handle saving the 'schedule' field as well.
      const success = await updateCandidate(editedCandidate);

      if (!success) {
        throw new Error("Failed to update candidate's main data");
      }

      // Handle file uploads if any
      const uploadPromises = [];
      // Upload CV if selected
      if (cvFile) {
        // Create a consistent path format for CV files
        const fileExt = cvFile.name.split(".").pop();
        const cvFilePath = `${editedCandidate.storage_id}/cv.${fileExt}`;

        console.debug("Uploading CV to path:", cvFilePath);

        uploadPromises.push(
          supabase.storage
            .from("candidate-documents")
            .upload(cvFilePath, cvFile, { upsert: true })
            .then(async (result) => {
              if (result.error) {
                console.error("Error uploading CV:", result.error);
                throw result.error;
              }

              // Verify that we can create a signed URL for the file
              const { data: signedUrlData, error: urlError } =
                await supabase.storage
                  .from("candidate-documents")
                  .createSignedUrl(cvFilePath, 3600);

              if (urlError) {
                console.error("Error creating signed URL:", urlError);
                throw urlError;
              }

              // Update candidate with CV path (store just the path, not the full URL)
              const updatedFormData = { ...editedCandidate.form_data };
              updatedFormData.cv = cvFilePath;

              const { error: updateError } = await supabase
                .from("candidates")
                .update({
                  form_data: updatedFormData,
                })
                .eq("id", editedCandidate.id);

              if (updateError) {
                console.error(
                  "Error updating candidate with CV path:",
                  updateError
                );
                throw updateError;
              }

              console.log("Successfully updated candidate with CV path");

              toast({
                title: "Επιτυχία",
                description: "Το CV ανέβηκε επιτυχώς.",
              });
            })
            .catch((error) => {
              console.error("Error in CV upload process:", error);
              toast({
                title: "Σφάλμα",
                description: "Υπήρξε πρόβλημα κατά την αποθήκευση του CV.",
                variant: "destructive",
              });
            })
        );
      }

      // Upload document files if any
      Object.entries(docFiles).forEach(([docType, file]) => {
        if (file) {
          const fileExt = file.name.split(".").pop();
          const filePath = `${editedCandidate.storage_id}/${docType}.${fileExt}`;

          uploadPromises.push(
            supabase.storage
              .from("candidate-documents")
              .upload(filePath, file, { upsert: true })
              .then(async (result) => {
                if (result.error) {
                  console.error(`Error uploading ${docType}:`, result.error);
                  throw result.error;
                }

                // Verify that we can create a signed URL for the file
                const { data: signedUrlData, error: urlError } =
                  await supabase.storage
                    .from("candidate-documents")
                    .createSignedUrl(filePath, 3600);

                if (urlError) {
                  console.error(
                    `Error creating signed URL for ${docType}:`,
                    urlError
                  );
                  throw urlError;
                }

                // Store just the path, not the full URL
                // Update candidate with document path
                const updatedFormData = { ...editedCandidate.form_data };
                updatedFormData[`${docType}`] = filePath;

                const { error: updateError } = await supabase
                  .from("candidates")
                  .update({
                    form_data: updatedFormData,
                  })
                  .eq("id", editedCandidate.id);

                if (updateError) {
                  console.error(
                    `Error updating candidate with ${docType} path:`,
                    updateError
                  );
                  throw updateError;
                }

                toast({
                  title: "Επιτυχία",
                  description: `Το ${docType} ανέβηκε επιτυχώς.`,
                });
              })
              .catch((error) => {
                console.error(`Error in ${docType} upload process:`, error);
                toast({
                  title: "Σφάλμα",
                  description: `Υπήρξε πρόβλημα κατά την αποθήκευση του ${docType}.`,
                  variant: "destructive",
                });
                throw error;
              })
          );
        }
      });

      // Wait for all uploads to complete
      if (uploadPromises.length > 0) {
        await Promise.all(uploadPromises);
      }
      // Check if price list fields were updated
      const priceListFieldsUpdated = Object.keys(
        candidate?.form_data || {}
      ).some(
        (key) =>
          key.startsWith("price_list_") &&
          JSON.stringify(candidate?.form_data?.[key]) !==
          JSON.stringify(editedCandidate?.form_data?.[key])
      );

      // Fetch updated candidate data using our service
      const updatedCandidate = await fetchCandidateById(
        editedCandidate.id.toString()
      );

      // Update local state
      if (updatedCandidate) {
        setCandidate(updatedCandidate);
        setEditedCandidate(JSON.parse(JSON.stringify(updatedCandidate))); // Ensure deep copy
      }

      toast({
        title: "Επιτυχία",
        description: "Το προφίλ του Νταντά ενημερώθηκε με επιτυχία.",
      });

      // Check if candidate is nanny or tutor or both
      const isNannyOrTutor =
        updatedCandidate?.is_nanny_approved ||
        updatedCandidate?.is_tutor_approved ||
        updatedCandidate?.form_data?.candidate_type === "nanny" ||
        updatedCandidate?.form_data?.candidate_type === "tutor" ||
        updatedCandidate?.form_data?.candidate_type === "both";

      // Show appropriate hints
      if (isNannyOrTutor && !priceListFieldsUpdated) {
        setShowCVHint(true);
        // Auto-hide after 10 seconds
        setTimeout(() => setShowCVHint(false), 10000);
      }

      if (priceListFieldsUpdated) {
        setShowPriceListHint(true);
        // Auto-hide after 10 seconds
        setTimeout(() => setShowPriceListHint(false), 20000);
      }

      setEditMode(false);
      setCvFile(null);
      setDocFiles({
        id_passport: null,
        criminal_record_document: null,
        driving_license_document: null,
        first_aid: null,
        intro_video: null,
        work_documents: null,
        profile_photo: null,
        education_documents: null,
        reference_letters: null,
        nanny_cv: null,
        price_list_file: null,
        client_cv_file: null,
      });
    } catch (error) {
      console.error("Error saving changes:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία ενημέρωσης προφίλ Νταντά.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // --- Availability Handlers ---
  // Helper function to format a Date object to YYYY-MM-DD string in local timezone
  const formatDateToYYYYMMDD = (date: Date): string => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0"); // getMonth() is 0-indexed
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const updateAvailabilityInEditedCandidate = (
    newAvailabilityData: Partial<Candidate["schedule"]>
  ) => {
    if (!editedCandidate) return;
    console.log("Updating availability:", newAvailabilityData);
    setEditedCandidate((prev) => {
      if (!prev) return null;
      const currentAvailability = prev.schedule || {};
      return {
        ...prev,
        schedule: {
          ...currentAvailability,
          ...newAvailabilityData,
        },
      };
    });
  };

  // Helper function to get all date strings in a range
  const getDatesInRange = (startDate: Date, endDate: Date): string[] => {
    const dates: string[] = [];
    let currentDate = new Date(
      startDate.getFullYear(),
      startDate.getMonth(),
      startDate.getDate()
    );
    const finalEndDate = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      endDate.getDate()
    );

    while (currentDate <= finalEndDate) {
      dates.push(formatDateToYYYYMMDD(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  };

  // Helper function to split an available range based on an unavailable period
  const splitAvailableRangeByUnavailablePeriod = (
    avRange: { from: string; to: string }, // The existing available range
    unavailableFromDate: Date, // Start of the new unavailable period
    unavailableToDate: Date // End of the new unavailable period
  ): { from: string; to: string }[] => {
    const newRanges: { from: string; to: string }[] = [];
    const avFromDate = new Date(avRange.from + "T00:00:00");
    const avToDate = new Date(avRange.to + "T00:00:00");

    // Normalize unavailable dates to midnight for clean comparison
    const unFrom = new Date(
      unavailableFromDate.getFullYear(),
      unavailableFromDate.getMonth(),
      unavailableFromDate.getDate()
    );
    const unTo = new Date(
      unavailableToDate.getFullYear(),
      unavailableToDate.getMonth(),
      unavailableToDate.getDate()
    );

    // Case 1: No overlap - available range is entirely before or after unavailable period
    if (avToDate < unFrom || avFromDate > unTo) {
      newRanges.push(avRange); // Keep the original available range
      return newRanges;
    }

    // Case 2: Overlap exists.
    // Part 1: Portion of available range before the unavailable period
    if (avFromDate < unFrom) {
      const dayBeforeUnavailable = new Date(unFrom);
      dayBeforeUnavailable.setDate(unFrom.getDate() - 1);
      if (dayBeforeUnavailable >= avFromDate) {
        // Ensure the new range is valid (end >= start)
        newRanges.push({
          from: formatDateToYYYYMMDD(avFromDate),
          to: formatDateToYYYYMMDD(dayBeforeUnavailable),
        });
      }
    }

    // Part 2: Portion of available range after the unavailable period
    if (avToDate > unTo) {
      const dayAfterUnavailable = new Date(unTo);
      dayAfterUnavailable.setDate(unTo.getDate() + 1);
      if (dayAfterUnavailable <= avToDate) {
        // Ensure the new range is valid (start <= end)
        newRanges.push({
          from: formatDateToYYYYMMDD(dayAfterUnavailable),
          to: formatDateToYYYYMMDD(avToDate),
        });
      }
    }
    // If the available range was completely consumed by the unavailable period, newRanges will be empty.
    return newRanges;
  };

  const handleAddAvailableRange = (range: DateRange) => {
    if (!range.from || !range.to) return;

    const datesToMakeAvailable = getDatesInRange(range.from, range.to);
    const currentExplicitlyUnavailable =
      editedCandidate?.schedule?.explicitlyUnavailable || [];
    const updatedExplicitlyUnavailable = currentExplicitlyUnavailable.filter(
      (d) => !datesToMakeAvailable.includes(d)
    );

    const newRange = {
      from: formatDateToYYYYMMDD(range.from),
      to: formatDateToYYYYMMDD(range.to),
    };
    const currentRanges = editedCandidate?.schedule?.availableRanges || [];

    updateAvailabilityInEditedCandidate({
      availableRanges: [...currentRanges, newRange],
      explicitlyUnavailable: updatedExplicitlyUnavailable,
    });
  };

  const handleRemoveAvailableRange = (indexToRemove: number) => {
    const currentRanges = editedCandidate?.schedule?.availableRanges || [];
    updateAvailabilityInEditedCandidate({
      availableRanges: currentRanges.filter(
        (_, index) => index !== indexToRemove
      ),
    });
  };

  const handleMarkRangeAsUnavailable = (range: DateRange) => {
    if (!range.from || !range.to) return;

    const datesToMakeUnavailable = getDatesInRange(range.from, range.to);
    const currentUnavailable =
      editedCandidate?.schedule?.explicitlyUnavailable || [];
    // Combine and remove duplicates
    const newExplicitlyUnavailable = Array.from(
      new Set([...currentUnavailable, ...datesToMakeUnavailable])
    );

    // Adjust availableRanges
    const currentAvailableRanges =
      editedCandidate?.schedule?.availableRanges || [];
    const resultingAvailableRanges: { from: string; to: string }[] = [];

    const unavailableFromDate = range.from; // Date object from DateRange
    const unavailableToDate = range.to; // Date object from DateRange

    currentAvailableRanges.forEach((avRangeString) => {
      const splitRanges = splitAvailableRangeByUnavailablePeriod(
        avRangeString,
        unavailableFromDate,
        unavailableToDate
      );
      resultingAvailableRanges.push(...splitRanges);
    });

    updateAvailabilityInEditedCandidate({
      availableRanges: resultingAvailableRanges,
      explicitlyUnavailable: newExplicitlyUnavailable,
    });
  };

  const handleToggleUnavailableDate = (date: Date) => {
    const dateString = formatDateToYYYYMMDD(date);
    const currentExplicitlyUnavailable =
      editedCandidate?.schedule?.explicitlyUnavailable || [];
    const isCurrentlyUnavailable =
      currentExplicitlyUnavailable.includes(dateString);

    let newExplicitlyUnavailable: string[];
    let newAvailableRanges = editedCandidate?.schedule?.availableRanges || [];

    if (isCurrentlyUnavailable) {
      // Making it AVAILABLE: Remove from explicitlyUnavailable
      newExplicitlyUnavailable = currentExplicitlyUnavailable.filter(
        (d) => d !== dateString
      );
      // No direct changes to availableRanges, user would use "Add Available Range"
    } else {
      // Making it UNAVAILABLE: Add to explicitlyUnavailable
      newExplicitlyUnavailable = Array.from(
        new Set([...currentExplicitlyUnavailable, dateString])
      );

      // Adjust availableRanges
      const currentAvRanges = editedCandidate?.schedule?.availableRanges || [];
      const tempAvailableRanges: { from: string; to: string }[] = [];
      const targetDateObj = new Date(dateString + "T00:00:00"); // The single day being marked unavailable

      currentAvRanges.forEach((avRangeString) => {
        const splitRanges = splitAvailableRangeByUnavailablePeriod(
          avRangeString,
          targetDateObj, // Unavailable period is just this single day
          targetDateObj
        );
        tempAvailableRanges.push(...splitRanges);
      });
      newAvailableRanges = tempAvailableRanges;
    }

    updateAvailabilityInEditedCandidate({
      availableRanges: newAvailableRanges,
      explicitlyUnavailable: newExplicitlyUnavailable,
    });
  };

  const handleRemoveUnavailableDateRange = (rangeToRemove: DateRange) => {
    if (
      !rangeToRemove.from ||
      !rangeToRemove.to ||
      !editedCandidate?.schedule?.explicitlyUnavailable
    )
      return;

    const datesInRangeToRemove: string[] = [];
    // Create date objects ensuring they are treated as local dates at midnight
    let currentDate = new Date(
      rangeToRemove.from.getFullYear(),
      rangeToRemove.from.getMonth(),
      rangeToRemove.from.getDate()
    );
    const endDate = new Date(
      rangeToRemove.to.getFullYear(),
      rangeToRemove.to.getMonth(),
      rangeToRemove.to.getDate()
    );

    while (currentDate <= endDate) {
      datesInRangeToRemove.push(formatDateToYYYYMMDD(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    const currentUnavailable = editedCandidate.schedule.explicitlyUnavailable;
    const updatedUnavailable = currentUnavailable.filter(
      (d) => !datesInRangeToRemove.includes(d)
    );
    updateAvailabilityInEditedCandidate({
      explicitlyUnavailable: updatedUnavailable,
    });
  };
  // --- End Availability Handlers ---

  if (loading) {
    return (
      <div className="w-full max-w-[1920px] mx-auto py-6 px-4 md:px-6 lg:px-8 xl:px-12 flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!candidate) {
    return (
      <div className="w-full max-w-[1920px] mx-auto py-6 px-4 md:px-6 lg:px-8 xl:px-12">
        <Alert variant="destructive">
          <AlertDescription>
            Η υποψήφιος δεν βρέθηκε ή έχει διαγραφεί.
          </AlertDescription>
        </Alert>
        <Button
          variant="ghost"
          className="flex items-center gap-1 mt-4 hover:bg-primary/10 transition-colors"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Επιστροφή</span>
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full max-w-[1920px] mx-auto py-6 space-y-8 px-4 md:px-6 lg:px-8 xl:px-12">
      {candidate && (
        <ProfileHeader
          profile={{
            id: candidate.id,
            name: candidate.form_data?.name,
            surname: candidate.form_data?.surname,
            form_data: candidate.form_data || {},
            city: candidate.form_data?.city,
            address: candidate.form_data?.address,
            position_interests: candidate.form_data?.position_interests,
            profile_photo: candidate.form_data?.profile_photo,
            is_rejected: candidate.is_rejected,
            type:
              !candidate.is_nanny_approved && !candidate.is_tutor_approved
                ? "candidate"
                : candidate.is_nanny_approved
                  ? "nanny"
                  : "tutor",
            is_nanny_approved: candidate.is_nanny_approved,
            is_tutor_approved: candidate.is_tutor_approved,
          }}
          editMode={editMode}
          saving={saving}
          isNannyUser={isNannyUser}
          hideBackButton={isNannyUser}
          onEdit={handleEditCandidate}
          onCancel={handleCancelEdit}
          onSave={handleSaveChanges}
          onBack={() => navigate(-1)}
          onRefresh={loadCandidate}
          // Pass permission flags to control UI elements
          permissions={{
            canEdit: isNannyUser
              ? true
              : candidate.is_nanny_approved
                ? canEdit("nannies")
                : candidate.is_tutor_approved
                  ? canEdit("tutors")
                  : canEdit("candidates"),
            canCreateDeal: canCreate("deals"),
            canApproveNanny: hasSpecificPermission(
              PERMISSION_CODES.put_nannies
            ),
            canApproveTutor: hasSpecificPermission(PERMISSION_CODES.put_tutors),
            canRejectCandidate: hasSpecificPermission(
              PERMISSION_CODES.put_candidates
            ),
          }}
        />
      )}

      {/* CV Generation Hint */}
      {showCVHint && !isNannyUser && (
        <Alert className="mb-4 border-primary/30 bg-primary/5 relative">
          <FileText className="h-5 w-5 text-primary" />
          <AlertTitle className="text-primary">
            Δημιουργία Βιογραφικού
          </AlertTitle>
          <AlertDescription className="text-primary/80">
            Το προφίλ ενημερώθηκε επιτυχώς. Θέλετε να δημιουργήσετε ένα νέο
            βιογραφικό με τα ενημερωμένα στοιχεία;
          </AlertDescription>
          <div className="mt-3">
            <Button
              onClick={async () => {
                try {
                  await apiClient.put(
                    `/candidates/${candidate.id}/generate-file`,
                    {
                      file_type: "cv",
                    }
                  );
                  toast({
                    title: "Επιτυχία",
                    description: "Το Βιογραφικό δημιουργήθηκε με επιτυχία.",
                  });
                  setShowCVHint(false);
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                } catch (error) {
                  console.error("Error generating CV:", error);
                  toast({
                    title: "Σφάλμα",
                    description: "Απέτυχε η δημιουργία του Βιογραφικού.",
                    variant: "destructive",
                  });
                }
              }}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/10"
            >
              Δημιουργία Βιογραφικού
            </Button>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-6 w-6 rounded-full p-0 text-primary/70 hover:bg-primary/10"
            onClick={() => setShowCVHint(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </Alert>
      )}

      {/* Price List PDF Generation Hint */}
      {showPriceListHint && (
        <Alert className="mb-4 border-primary/30 bg-primary/5 relative">
          <FileSpreadsheet className="h-5 w-5 text-primary" />
          <AlertTitle className="text-primary">
            Δημιουργία Τιμοκαταλόγου PDF
          </AlertTitle>
          <AlertDescription className="text-primary/80">
            Ο τιμοκατάλογος ενημερώθηκε επιτυχώς. Θέλετε να δημιουργήσετε ένα
            PDF με τον ενημερωμένο τιμοκατάλογο;
          </AlertDescription>
          <div className="mt-3">
            <Button
              onClick={async () => {
                try {
                  await apiClient.put(
                    `/candidates/${candidate.id}/generate-file`,
                    {
                      file_type: "price_list",
                    }
                  );
                  toast({
                    title: "Επιτυχία",
                    description:
                      "Το PDF του τιμοκαταλόγου δημιουργήθηκε με επιτυχία.",
                  });
                  setShowPriceListHint(false);
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                } catch (error) {
                  console.error("Error generating price list PDF:", error);
                  toast({
                    title: "Σφάλμα",
                    description:
                      "Απέτυχε η δημιουργία του PDF του τιμοκαταλόγου.",
                    variant: "destructive",
                  });
                }
              }}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/10"
            >
              Δημιουργία PDF Τιμοκαταλόγου
            </Button>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-6 w-6 rounded-full p-0 text-primary/70 hover:bg-primary/10"
            onClick={() => setShowPriceListHint(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </Alert>
      )}

      {/* Hidden file inputs */}
      <input
        type="file"
        ref={fileInputRefs.cv}
        className="hidden"
        onChange={(e) => handleFileChange("cv", e)}
        accept=".pdf,.doc,.docx"
      />
      <input
        type="file"
        ref={fileInputRefs.id_passport}
        className="hidden"
        onChange={(e) => handleFileChange("id_passport", e)}
        accept=".pdf,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.criminal_record_document}
        className="hidden"
        onChange={(e) => handleFileChange("criminal_record_document", e)}
        accept=".pdf,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.driving_license_document}
        className="hidden"
        onChange={(e) => handleFileChange("driving_license_document", e)}
        accept=".pdf,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.first_aid}
        className="hidden"
        onChange={(e) => handleFileChange("first_aid", e)}
        accept=".pdf,.jpg,.jpeg,.png"
      />

      <input
        type="file"
        ref={fileInputRefs.intro_video}
        className="hidden"
        onChange={(e) => handleFileChange("intro_video", e)}
        accept=".mp4,.mov,.avi"
      />

      <input
        type="file"
        ref={fileInputRefs.profile_photo}
        className="hidden"
        onChange={(e) => handleFileChange("profile_photo", e)}
        accept=".jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.education_documents}
        className="hidden"
        onChange={(e) => handleFileChange("education_documents", e)}
        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.reference_letters}
        className="hidden"
        onChange={(e) => handleFileChange("reference_letters", e)}
        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.nanny_cv}
        className="hidden"
        onChange={(e) => handleFileChange("nanny_cv", e)}
        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.price_list_file}
        className="hidden"
        onChange={(e) => handleFileChange("price_list_file", e)}
        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
      />
      <input
        type="file"
        ref={fileInputRefs.client_cv_file}
        className="hidden"
        onChange={(e) => handleFileChange("client_cv_file", e)}
        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
      />

      {/* Main Content with Tabs */}
      <Tabs defaultValue="profile" className="mt-6">
        <TabsList
          className={`w-full sm:w-auto grid ${isNannyUser ? "grid-cols-4" : "grid-cols-6"
            } h-9 bg p-1 rounded-lg float-right mb-4`}
        >
          <TabsTrigger
            value="profile"
            className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
          >
            Προφίλ
          </TabsTrigger>
          <TabsTrigger
            value="files"
            className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
          >
            Αρχεία
          </TabsTrigger>
          <TabsTrigger
            value="cv"
            className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
          >
            CV
          </TabsTrigger>
          {!isNannyUser && (
            <TabsTrigger
              value="price-list"
              className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
            >
              Τιμοκατάλογος
            </TabsTrigger>
          )}

          <TabsTrigger
            value="deals"
            className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
          >
            Deals
          </TabsTrigger>
          {!isNannyUser && (
            <TabsTrigger
              value="comments"
              className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
            >
              Σχόλια
            </TabsTrigger>
          )}
        </TabsList>

        <div className="clear-both"></div>

        {/* Deals Tab Content */}
        <TabsContent value="deals">
          <Card variant="accent" className="p-4 mt-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-medium text-primary">
                  Συμφωνίες (Deals)
                </h3>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-primary/20">
                    <th className="text-left py-2 font-medium text-primary">
                      ID
                    </th>
                    <th className="text-left py-2 font-medium text-primary">
                      Πελάτης
                    </th>
                    {!isNannyUser && (
                      <>
                        <th className="text-left py-2 font-medium text-primary">
                          Κατάσταση
                        </th>
                        <th className="text-left py-2 font-medium text-primary">
                          Ιστορικό
                        </th>
                      </>
                    )}
                    <th className="text-left py-2 font-medium text-primary">
                      Ημερομηνία
                    </th>
                    {!isNannyUser && (
                      <th className="text-left py-2 font-medium text-primary">
                        Καθαρό Ποσό
                      </th>
                    )}
                    <th className="text-left py-2 font-medium text-primary">
                      Ποσό
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {dealsLoading ? (
                    <tr>
                      <td colSpan={5} className="py-4 text-center">
                        <div className="flex justify-center">
                          <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent"></div>
                        </div>
                      </td>
                    </tr>
                  ) : deals && deals.length > 0 ? (
                    deals.map((deal: Deal) => (
                      <tr
                        key={deal.id}
                        className="border-t border-primary/10 hover:bg-primary/5 cursor-pointer"
                        onClick={() =>
                          isNannyUser ? {} : navigate(`/deals/${deal.id}`)
                        }
                      >
                        <td className="py-3">
                          <span className="font-medium text-primary">
                            {`DL-${deal.id.toString().substring(0, 5)}`}
                          </span>
                        </td>
                        <td className="py-3">
                          {deal.client_father_name || "Μη διαθέσιμο"}{" "}
                          {deal.client_mother_name || ""}
                        </td>
                        {!isNannyUser && (
                          <>
                            <td className="py-3">
                              <Badge
                                variant={
                                  deal.status === DealStatus.Active
                                    ? "success"
                                    : deal.status === DealStatus.InProgress
                                      ? "outline"
                                      : deal.status === DealStatus.Rejected
                                        ? "destructive"
                                        : "secondary"
                                }
                              >
                                {deal.status}
                              </Badge>
                            </td>

                            <td className="py-3">
                              <div className="w-[200px] px-1">
                                <CompactTimeline history={deal.history} />
                              </div>
                            </td>
                          </>
                        )}

                        <td className="py-3">{deal.deal_date || ""}</td>
                        {!isNannyUser && (
                          <td className="py-3">
                            {deal.revenue - deal.candidate_salary} €
                          </td>
                        )}
                        <td className="py-3">
                          {isNannyUser
                            ? `${deal.candidate_salary} €`
                            : `${deal.revenue} €`}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr className="border-t">
                      <td
                        colSpan={5}
                        className="py-8 text-center text-sm text-muted-foreground"
                      >
                        Δεν υπάρχουν deals για αυτόν τον υποψήφιο.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </Card>
        </TabsContent>

        {/* Profile Tab Content */}
        <TabsContent value="profile">
          <ProfileTab
            candidate={candidate}
            editedCandidate={editedCandidate}
            editMode={editMode}
            isNannyUser={isNannyUser}
            handleInputChange={handleInputChange}
            handlePositionInterestToggle={handlePositionInterestToggle}
            onAddAvailableRange={handleAddAvailableRange}
            onRemoveAvailableRange={handleRemoveAvailableRange}
            onMarkRangeAsUnavailable={handleMarkRangeAsUnavailable}
            onRemoveUnavailableDateRange={handleRemoveUnavailableDateRange}
            onToggleUnavailableDate={handleToggleUnavailableDate}
          />
        </TabsContent>

        {/* Files Tab Content */}
        <TabsContent value="files">
          <FilesTab
            candidate={candidate}
            editedCandidate={editedCandidate}
            editMode={editMode}
            handleFilePreview={handleFilePreview}
            handleFileDownload={handleFileDownload}
            handleInputChange={handleInputChange}
            triggerFileInput={triggerFileInput}
            fileInputRefs={fileInputRefs}
            docFiles={docFiles}
            isNannyUser={isNannyUser}
          />
        </TabsContent>

        {/* CV Tab Content */}
        <TabsContent value="cv">
          <CVTab
            candidate={candidate}
            editedCandidate={editedCandidate}
            editMode={editMode}
            handleInputChange={handleInputChange}
            handleFilePreview={handleFilePreview}
            handleFileDownload={handleFileDownload}
            getFilePreviewUrl={getFilePreviewUrl}
            triggerFileInput={triggerFileInput}
            fileInputRefs={fileInputRefs}
            isNannyUser={isNannyUser}
          />
        </TabsContent>

        {/* Price List Tab Content */}
        {!isNannyUser && (
          <TabsContent value="price-list">
            <PriceListTab
              candidate={candidate}
              editedCandidate={editedCandidate}
              editMode={editMode}
              handleInputChange={handleInputChange}
            />
          </TabsContent>
        )}

        {/* Comments Tab Content */}
        {!isNannyUser && (
          <TabsContent value="comments">
            <CommentsTab candidate={candidate} editMode={editMode} />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default NannyProfile;
